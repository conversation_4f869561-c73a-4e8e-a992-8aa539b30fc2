# مثال على ملف متغيرات البيئة - Environment Variables Example
# انسخ هذا الملف إلى .env وأدخل القيم الحقيقية
# Copy this file to .env and enter real values

# =============================================================================
# مفتاح Gemini API (مطلوب) - Gemini API Key (Required)
# احصل عليه من: https://ai.google.dev/
# =============================================================================
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# إعدادات أساسية - Basic Settings
# =============================================================================
PORT=8000
NODE_ENV=development
MAX_FILE_SIZE=10485760

# =============================================================================
# إعدادات Gemini API
# =============================================================================
GEMINI_MODEL=gemini-2.0-flash
GEMINI_TEMPERATURE=0.4
GEMINI_MAX_TOKENS=4096

# =============================================================================
# إعدادات الأمان
# =============================================================================
SESSION_SECRET=your_session_secret_here
ENABLE_HTTPS=false

# =============================================================================
# إعدادات التطوير
# =============================================================================
DEBUG=true
AUTO_RELOAD=true
LOG_LEVEL=info

# =============================================================================
# تعليمات الإعداد - Setup Instructions
# =============================================================================

# 1. انسخ هذا الملف: cp .env.example .env
# 2. احصل على مفتاح API من: https://ai.google.dev/
# 3. استبدل "your_gemini_api_key_here" بمفتاحك الحقيقي
# 4. احفظ الملف وشغل التطبيق

# ⚠️ تحذير: لا تشارك ملف .env مع أحد!
