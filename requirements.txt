# متطلبات الخادم الخلفي لتطبيق تحسين الصور
# Backend requirements for Image Enhancement App

# Flask web framework
Flask==2.3.3
Flask-CORS==4.0.0

# Image processing
Pillow==10.0.1
numpy==1.24.3

# Google AI SDK (optional - for real Gemini API integration)
google-generativeai==0.3.2

# Additional utilities
Werkzeug==2.3.7

# Environment variables support
python-dotenv==1.0.0

# Development dependencies (optional)
pytest==7.4.2
black==23.9.1
flake8==6.1.0
