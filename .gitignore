# ملفات متغيرات البيئة - Environment files
.env
.env.local
.env.*.local

# مفاتيح API وأسرار - API keys and secrets
*.key
*.pem
secrets/
config/secrets.json

# ملفات Python - Python files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ملفات Node.js - Node.js files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# ملفات IDE - IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# ملفات نظام التشغيل - OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ملفات السجل - Log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ملفات مؤقتة - Temporary files
tmp/
temp/
.tmp/
.temp/

# ملفات التخزين المؤقت - Cache files
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist

# ملفات قاعدة البيانات - Database files
*.db
*.sqlite
*.sqlite3

# ملفات الصور المرفوعة - Uploaded images
uploads/
images/temp/
enhanced_images/

# ملفات النسخ الاحتياطية - Backup files
*.bak
*.backup
*.old

# ملفات الاختبار - Test files
coverage/
.nyc_output/
.coverage
htmlcov/

# ملفات التوزيع - Distribution files
dist/
build/

# ملفات التكوين المحلية - Local config files
config.local.js
settings.local.json

# ملفات الشهادات - Certificate files
*.crt
*.cert
*.ca-bundle

# مجلدات خاصة بالمشروع - Project specific folders
.venv/
venv/
env/
ENV/

# ملفات التطوير - Development files
.pytest_cache/
.mypy_cache/
.dmypy.json
dmypy.json
