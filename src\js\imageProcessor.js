// Image Processing Module - Fixed Negative Issue
// معالج الصور - تم إصلاح مشكلة النيغاتيف

class ImageProcessor {
    constructor() {
        this.canvas = null;
        this.ctx = null;
    }

    // إنشاء canvas جديد
    createCanvas(width, height) {
        this.canvas = document.createElement('canvas');
        this.canvas.width = width;
        this.canvas.height = height;
        this.ctx = this.canvas.getContext('2d');
        
        // تعيين إعدادات عالية الجودة
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
        
        return this.canvas;
    }

    // تحسين الصورة مع إصلاح مشكلة النيغاتيف
    async enhanceImage(imageFile, options = {}) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => {
                try {
                    const scaleFactor = this.getScaleFactor(options.enhancementType);
                    const newWidth = img.width * scaleFactor;
                    const newHeight = img.height * scaleFactor;
                    
                    // إنشاء canvas
                    this.createCanvas(newWidth, newHeight);
                    
                    // رسم الصورة بحجم أكبر
                    this.ctx.drawImage(img, 0, 0, newWidth, newHeight);
                    
                    // تطبيق فلاتر التحسين (بدون عكس الألوان)
                    this.applyEnhancementFilters(options);
                    
                    // تحويل إلى base64
                    const enhancedImage = this.canvas.toDataURL('image/png', 1.0);
                    
                    resolve({
                        enhancedImage: enhancedImage,
                        originalSize: { width: img.width, height: img.height },
                        enhancedSize: { width: newWidth, height: newHeight },
                        enhancementFactor: options.enhancementType
                    });
                    
                } catch (error) {
                    reject(error);
                }
            };
            
            img.onerror = () => {
                reject(new Error('فشل في تحميل الصورة'));
            };
            
            // تحميل الصورة
            if (typeof imageFile === 'string') {
                img.src = imageFile;
            } else {
                const reader = new FileReader();
                reader.onload = (e) => {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(imageFile);
            }
        });
    }

    // تحديد معامل التكبير
    getScaleFactor(enhancementType) {
        switch (enhancementType) {
            case '4x': return 4;
            case '2x': return 2;
            case 'auto': return 2;
            default: return 2;
        }
    }

    // تطبيق فلاتر التحسين (مُصححة)
    applyEnhancementFilters(options) {
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const data = imageData.data;
        
        // تطبيق فلاتر التحسين بدون عكس الألوان
        this.applySharpenFilter(data, this.canvas.width, this.canvas.height);
        this.applyContrastFilter(data, 1.1);
        this.applyBrightnessFilter(data, 1.05);
        
        // إعادة رسم البيانات المحسنة
        this.ctx.putImageData(imageData, 0, 0);
    }

    // فلتر تحسين الحدة (مُصحح)
    applySharpenFilter(data, width, height) {
        const output = new Uint8ClampedArray(data);
        const sharpenKernel = [
            0, -1, 0,
            -1, 5, -1,
            0, -1, 0
        ];
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                for (let c = 0; c < 3; c++) { // RGB فقط، تجاهل Alpha
                    let sum = 0;
                    
                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const idx = ((y + ky) * width + (x + kx)) * 4 + c;
                            const kernelIdx = (ky + 1) * 3 + (kx + 1);
                            sum += data[idx] * sharpenKernel[kernelIdx];
                        }
                    }
                    
                    const currentIdx = (y * width + x) * 4 + c;
                    // تطبيق التحسين مع الحفاظ على القيم الأصلية
                    output[currentIdx] = Math.min(255, Math.max(0, sum));
                }
                
                // الحفاظ على قناة Alpha
                const alphaIdx = (y * width + x) * 4 + 3;
                output[alphaIdx] = data[alphaIdx];
            }
        }
        
        // نسخ النتيجة
        for (let i = 0; i < data.length; i++) {
            data[i] = output[i];
        }
    }

    // فلتر تحسين التباين (مُصحح)
    applyContrastFilter(data, contrast = 1.1) {
        const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255));
        
        for (let i = 0; i < data.length; i += 4) {
            // تطبيق التباين على RGB فقط
            data[i] = Math.min(255, Math.max(0, factor * (data[i] - 128) + 128));     // Red
            data[i + 1] = Math.min(255, Math.max(0, factor * (data[i + 1] - 128) + 128)); // Green
            data[i + 2] = Math.min(255, Math.max(0, factor * (data[i + 2] - 128) + 128)); // Blue
            // تجاهل Alpha (data[i + 3])
        }
    }

    // فلتر تحسين السطوع (مُصحح)
    applyBrightnessFilter(data, brightness = 1.05) {
        const adjustment = (brightness - 1) * 50; // تقليل التأثير
        
        for (let i = 0; i < data.length; i += 4) {
            // تطبيق السطوع على RGB فقط
            data[i] = Math.min(255, Math.max(0, data[i] + adjustment));     // Red
            data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + adjustment)); // Green
            data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + adjustment)); // Blue
            // تجاهل Alpha (data[i + 3])
        }
    }

    // تنظيف الذاكرة
    cleanup() {
        if (this.canvas) {
            this.canvas.width = 0;
            this.canvas.height = 0;
            this.canvas = null;
            this.ctx = null;
        }
    }
}

// تصدير الفئة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageProcessor;
} else {
    window.ImageProcessor = ImageProcessor;
}
