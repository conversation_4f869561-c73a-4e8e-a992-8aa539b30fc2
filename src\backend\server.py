#!/usr/bin/env python3
"""
Enhanced Backend Server for Gemini AI Image Enhancer
خادم خلفي محسن لمحسن الصور بـ Gemini AI
"""

import os
import sys
import json
import base64
import io
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    print("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")
    print("⚠️  Using system environment variables only")

# Flask for web server
try:
    from flask import Flask, request, jsonify, send_from_directory, render_template
    from flask_cors import CORS
except ImportError:
    print("❌ Flask not installed. Install with: pip install Flask Flask-CORS")
    sys.exit(1)

# PIL for image processing
try:
    from PIL import Image, ImageEnhance, ImageFilter
except ImportError:
    print("❌ Pillow not installed. Install with: pip install Pillow")
    sys.exit(1)

# Google AI SDK
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
    print("✅ Google Generative AI SDK available")
except ImportError:
    GEMINI_AVAILABLE = False
    print("⚠️  Google Generative AI SDK not available. Install with: pip install google-generativeai")

# Initialize Flask app
app = Flask(__name__, 
           static_folder='../../',
           template_folder='../../')
CORS(app)

# Configuration with environment variables
CONFIG = {
    'UPLOAD_FOLDER': os.getenv('UPLOAD_FOLDER', 'uploads'),
    'MAX_CONTENT_LENGTH': int(os.getenv('MAX_FILE_SIZE', 16 * 1024 * 1024)),
    'ALLOWED_EXTENSIONS': {'png', 'jpg', 'jpeg', 'webp'},
    'GEMINI_MODEL': os.getenv('GEMINI_MODEL', 'gemini-2.0-flash'),
    'GEMINI_API_KEY': os.getenv('GEMINI_API_KEY'),
    'PORT': int(os.getenv('PORT', 5000)),
    'DEBUG': os.getenv('DEBUG', 'false').lower() == 'true',
    'HOST': os.getenv('HOST', '0.0.0.0')
}

app.config.update(CONFIG)

# Ensure upload directory exists
upload_dir = Path(CONFIG['UPLOAD_FOLDER'])
upload_dir.mkdir(exist_ok=True)

class ImageEnhancer:
    """Enhanced Image Processing Class"""

    def __init__(self, api_key: Optional[str] = None):
        # Use provided API key or get from environment
        self.api_key = api_key or CONFIG['GEMINI_API_KEY']
        
        if self.api_key and GEMINI_AVAILABLE:
            genai.configure(api_key=self.api_key)
            print(f"✅ Gemini API configured with key: {self.api_key[:10]}...")
        elif not self.api_key:
            print("⚠️  No Gemini API key found in environment variables")
        elif not GEMINI_AVAILABLE:
            print("⚠️  Gemini SDK not available")

    def allowed_file(self, filename: str) -> bool:
        """Check if file extension is allowed"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in CONFIG['ALLOWED_EXTENSIONS']

    async def enhance_image_with_gemini(self, image_data: bytes, enhancement_type: str) -> Dict[str, Any]:
        """Enhance image using Gemini AI analysis"""
        if not self.api_key or not GEMINI_AVAILABLE:
            raise Exception("Gemini API not available")

        try:
            # Load image
            image = Image.open(io.BytesIO(image_data))
            
            # Create model
            model = genai.GenerativeModel(CONFIG['GEMINI_MODEL'])
            
            # Generate analysis prompt
            prompt = self._generate_analysis_prompt(enhancement_type)
            
            # Note: Gemini doesn't directly support image enhancement yet
            # So we'll use local enhancement with AI-guided parameters
            
            # For now, use advanced local enhancement
            enhanced_image = self._enhance_image_locally(image, enhancement_type)
            
            # Convert to base64
            buffer = io.BytesIO()
            enhanced_image.save(buffer, format='PNG', quality=100)
            enhanced_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return {
                'success': True,
                'enhanced_image': f'data:image/png;base64,{enhanced_base64}',
                'original_size': {'width': image.width, 'height': image.height},
                'enhanced_size': {'width': enhanced_image.width, 'height': enhanced_image.height},
                'enhancement_type': enhancement_type,
                'processing_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Gemini enhancement error: {e}")
            raise e

    def _enhance_image_locally(self, image: Image.Image, enhancement_type: str) -> Image.Image:
        """Enhanced local image processing"""
        
        # Determine scale factor
        scale_factors = {'2x': 2, '4x': 4, 'auto': 2}
        scale_factor = scale_factors.get(enhancement_type, 2)
        
        # Calculate new dimensions
        new_width = image.width * scale_factor
        new_height = image.height * scale_factor
        
        # High-quality resize using LANCZOS
        enhanced = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Apply enhancement filters
        enhanced = self._apply_enhancement_filters(enhanced, enhancement_type)
        
        return enhanced

    def _apply_enhancement_filters(self, image: Image.Image, enhancement_type: str) -> Image.Image:
        """Apply enhancement filters based on type"""
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Apply sharpening
        sharpness_factor = 1.3 if enhancement_type == '4x' else 1.2
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(sharpness_factor)
        
        # Apply contrast enhancement
        contrast_factor = 1.15 if enhancement_type == '4x' else 1.1
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(contrast_factor)
        
        # Apply color enhancement
        color_factor = 1.1 if enhancement_type in ['4x', 'auto'] else 1.05
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(color_factor)
        
        # Apply brightness adjustment (subtle)
        brightness_factor = 1.02
        enhancer = ImageEnhance.Brightness(image)
        image = enhancer.enhance(brightness_factor)
        
        # Apply noise reduction for 4x enhancement
        if enhancement_type == '4x':
            image = image.filter(ImageFilter.SMOOTH_MORE)
        
        return image

    def _generate_analysis_prompt(self, enhancement_type: str) -> str:
        """Generate prompt for Gemini analysis"""
        prompts = {
            '2x': "Analyze this image for 2x enhancement. Suggest optimal sharpness, contrast, and color adjustments.",
            '4x': "Analyze this image for 4x enhancement. Provide detailed recommendations for maximum quality improvement.",
            'auto': "Analyze this image and determine the best enhancement strategy automatically."
        }
        return prompts.get(enhancement_type, prompts['auto'])

# Routes
@app.route('/')
def index():
    """Serve the main application"""
    return send_from_directory('../../', 'index_new.html')

@app.route('/api/enhance-image', methods=['POST'])
def enhance_image():
    """Enhanced image processing endpoint"""
    try:
        # Check if file is present
        if 'image' not in request.files:
            return jsonify({'error': 'لا توجد صورة في الطلب'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'لم يتم اختيار ملف'}), 400
        
        # Get enhancement parameters
        enhancement_type = request.form.get('enhancementType', 'auto')
        api_key = request.form.get('apiKey')
        
        # Initialize enhancer
        enhancer = ImageEnhancer(api_key)
        
        # Check file type
        if not enhancer.allowed_file(file.filename):
            return jsonify({'error': 'نوع الملف غير مدعوم'}), 400
        
        # Read image data
        image_data = file.read()
        
        # Enhance image
        result = enhancer.enhance_image_with_gemini(image_data, enhancement_type)
        
        return jsonify(result)
        
    except Exception as e:
        print(f"Enhancement error: {e}")
        return jsonify({'error': f'خطأ في معالجة الصورة: {str(e)}'}), 500

@app.route('/api/validate-key', methods=['POST'])
def validate_api_key():
    """Validate Gemini API key"""
    try:
        data = request.get_json()
        api_key = data.get('apiKey')
        
        if not api_key:
            return jsonify({'valid': False, 'error': 'مفتاح API مطلوب'}), 400
        
        if not GEMINI_AVAILABLE:
            return jsonify({'valid': False, 'error': 'Gemini SDK غير متوفر'}), 500
        
        # Test the API key
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel(CONFIG['GEMINI_MODEL'])
        
        # Simple test
        response = model.generate_content("Hello")
        
        return jsonify({'valid': True, 'message': 'مفتاح API صحيح'})
        
    except Exception as e:
        return jsonify({'valid': False, 'error': f'مفتاح API غير صحيح: {str(e)}'}), 400

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'gemini_available': GEMINI_AVAILABLE,
        'api_key_configured': bool(CONFIG['GEMINI_API_KEY'])
    })

# Static file serving
@app.route('/src/<path:filename>')
def serve_static(filename):
    """Serve static files from src directory"""
    return send_from_directory('../../src', filename)

# Error handlers
@app.errorhandler(413)
def too_large(e):
    """Handle large files"""
    return jsonify({'error': 'حجم الملف كبير جداً (الحد الأقصى 16MB)'}), 413

@app.errorhandler(404)
def not_found(e):
    """Handle not found"""
    return jsonify({'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(e):
    """Handle internal errors"""
    return jsonify({'error': 'خطأ داخلي في الخادم'}), 500

if __name__ == '__main__':
    print("🚀 Starting Enhanced Gemini AI Image Enhancer Server...")
    print(f"📍 Server will run on: http://{CONFIG['HOST']}:{CONFIG['PORT']}")
    print(f"🔧 Debug mode: {CONFIG['DEBUG']}")
    print(f"🤖 Gemini API available: {GEMINI_AVAILABLE}")
    print(f"🔑 API key configured: {bool(CONFIG['GEMINI_API_KEY'])}")
    
    app.run(
        host=CONFIG['HOST'],
        port=CONFIG['PORT'],
        debug=CONFIG['DEBUG']
    )
