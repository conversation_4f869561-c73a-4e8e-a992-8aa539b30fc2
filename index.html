<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محسن جودة الصور بالذكاء الاصطناعي - جيميني</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    <i class="fas fa-magic"></i>
                    محسن جودة الصور بالذكاء الاصطناعي
                </h1>
                <p class="subtitle">قم برفع جودة صورك باستخدام تقنية جيميني المتطورة</p>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Upload Section -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                        <h3>اسحب وأفلت الصورة هنا</h3>
                        <p>أو انقر لاختيار صورة من جهازك</p>
                        <input type="file" id="fileInput" accept="image/*" hidden>
                        <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open"></i>
                            اختر صورة
                        </button>
                    </div>
                </div>

                <!-- File Info -->
                <div class="file-info" id="fileInfo" style="display: none;">
                    <div class="file-details">
                        <i class="fas fa-image"></i>
                        <span id="fileName"></span>
                        <span id="fileSize"></span>
                    </div>
                    <button class="remove-btn" id="removeFile">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </section>

            <!-- Enhancement Options -->
            <section class="options-section" id="optionsSection" style="display: none;">
                <h3><i class="fas fa-cogs"></i> خيارات التحسين</h3>
                <div class="options-grid">
                    <div class="option-card">
                        <input type="radio" id="enhance2x" name="enhancement" value="2x" checked>
                        <label for="enhance2x">
                            <i class="fas fa-expand-arrows-alt"></i>
                            <span>تحسين 2x</span>
                            <small>مضاعفة الدقة</small>
                        </label>
                    </div>
                    <div class="option-card">
                        <input type="radio" id="enhance4x" name="enhancement" value="4x">
                        <label for="enhance4x">
                            <i class="fas fa-search-plus"></i>
                            <span>تحسين 4x</span>
                            <small>أربعة أضعاف الدقة</small>
                        </label>
                    </div>
                    <div class="option-card">
                        <input type="radio" id="enhanceAuto" name="enhancement" value="auto">
                        <label for="enhanceAuto">
                            <i class="fas fa-magic"></i>
                            <span>تحسين ذكي</span>
                            <small>تحسين تلقائي بالذكاء الاصطناعي</small>
                        </label>
                    </div>
                </div>

                <button class="enhance-btn" id="enhanceBtn">
                    <i class="fas fa-wand-magic-sparkles"></i>
                    ابدأ التحسين
                </button>
            </section>

            <!-- Progress Section -->
            <section class="progress-section" id="progressSection" style="display: none;">
                <div class="progress-container">
                    <div class="progress-info">
                        <i class="fas fa-robot"></i>
                        <h3>جاري تحسين الصورة بـ Gemini AI...</h3>
                        <p id="progressMessage">يرجى الانتظار بينما يقوم Gemini AI بتحليل وتحسين صورتك</p>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">0%</div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <h3><i class="fas fa-star"></i> النتائج</h3>
                <div class="comparison-container">
                    <div class="image-container">
                        <h4>الصورة الأصلية</h4>
                        <div class="image-wrapper">
                            <img id="originalImage" alt="الصورة الأصلية">
                            <div class="image-info">
                                <span id="originalSize"></span>
                            </div>
                        </div>
                    </div>
                    <div class="image-container">
                        <h4>الصورة المحسنة</h4>
                        <div class="image-wrapper">
                            <img id="enhancedImage" alt="الصورة المحسنة">
                            <div class="image-info">
                                <span id="enhancedSize"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="download-btn" id="downloadBtn">
                        <i class="fas fa-download"></i>
                        تحميل الصورة المحسنة
                    </button>
                    <button class="new-btn" id="newImageBtn">
                        <i class="fas fa-plus"></i>
                        صورة جديدة
                    </button>
                </div>
            </section>

            <!-- Error Section -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-container">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>حدث خطأ</h3>
                    <p id="errorMessage"></p>
                    <button class="retry-btn" id="retryBtn">
                        <i class="fas fa-redo"></i>
                        إعادة المحاولة
                    </button>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>مدعوم بتقنية <strong>Google Gemini 2.0 Flash AI</strong> | تحسين ذكي حقيقي للصور</p>
            <small>يستخدم Gemini API للتحليل الذكي + خوارزميات متقدمة للتحسين</small>
        </footer>
    </div>

    <!-- API Key Modal -->
    <div class="modal" id="apiKeyModal">
        <div class="modal-content">
            <h3><i class="fas fa-key"></i> إعداد مفتاح API</h3>
            <p>يرجى إدخال مفتاح Gemini API الخاص بك:</p>
            <input type="password" id="apiKeyInput" placeholder="أدخل مفتاح API هنا...">
            <div class="modal-buttons">
                <button class="save-btn" id="saveApiKey">حفظ</button>
                <button class="cancel-btn" id="cancelApiKey">إلغاء</button>
            </div>
            <div class="api-help">
                <p><i class="fas fa-info-circle"></i>
                   يمكنك الحصول على مفتاح API من
                   <a href="https://ai.google.dev/" target="_blank">Google AI Studio</a>
                </p>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>
