{"name": "gemini-image-enhancer", "version": "1.0.0", "description": "تطبيق ويب لرفع جودة الصور باستخدام الذكاء الاصطناعي جيميني - AI-powered image enhancement using Google Gemini", "main": "index.html", "scripts": {"start": "http-server -p 8000 -o", "dev": "live-server --port=8000 --open=/", "serve": "python server.py", "build": "echo 'No build process needed for this static app'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'", "format": "prettier --write *.html *.css *.js *.md"}, "keywords": ["gemini", "ai", "image-enhancement", "upscaling", "arabic", "google-ai", "image-processing", "web-app", "javascript", "html5", "css3"], "author": {"name": "Your Name", "email": "<EMAIL>", "url": "https://github.com/your-username"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/gemini-image-enhancer.git"}, "bugs": {"url": "https://github.com/your-username/gemini-image-enhancer/issues"}, "homepage": "https://github.com/your-username/gemini-image-enhancer#readme", "devDependencies": {"http-server": "^14.1.1", "live-server": "^1.2.2", "prettier": "^3.0.0"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "config": {"port": 8000, "host": "localhost"}, "directories": {"doc": "docs", "test": "tests"}, "files": ["index.html", "style.css", "script.js", "config.js", "README.md", "server.py"]}