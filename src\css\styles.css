/* Enhanced Image Enhancer Styles - Reorganized and Fixed */
/* أنماط محسن الصور - مُعاد تنظيمها ومُصححة */

/* ===== FONTS & VARIABLES ===== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

:root {
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #764ba2;
    --success-color: #48bb78;
    --error-color: #e53e3e;
    --warning-color: #ed8936;
    --info-color: #4299e1;
    
    --bg-primary: #f7fafc;
    --bg-secondary: #edf2f7;
    --bg-card: #ffffff;
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    
    --border-color: #e2e8f0;
    --border-radius: 12px;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.2);
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    direction: rtl;
}

/* ===== LAYOUT ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== CARDS ===== */
.card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--bg-secondary);
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

/* ===== UPLOAD AREA ===== */
.upload-area {
    border: 3px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 60px 30px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.upload-area:hover::before {
    opacity: 0.05;
}

.upload-area:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    transform: scale(1.02);
}

.upload-area.dragover::before {
    opacity: 0.1;
}

.upload-content {
    position: relative;
    z-index: 2;
}

.upload-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    display: block;
}

.upload-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;
}

.upload-hint {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.file-input {
    display: none;
}

/* ===== FILE INFO ===== */
.file-info {
    display: none;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, var(--success-color), #38a169);
    color: white;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin-top: 20px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-icon {
    font-size: 1.5rem;
}

.file-meta h4 {
    font-weight: 600;
    margin-bottom: 5px;
}

.file-meta p {
    opacity: 0.9;
    font-size: 0.9rem;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #38a169);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--error-color), #c53030);
    color: white;
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-icon {
    padding: 10px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
}

/* ===== ENHANCEMENT OPTIONS ===== */
.enhancement-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.option-card {
    position: relative;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    cursor: pointer;
    transition: var(--transition);
    background: var(--bg-card);
}

.option-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.option-card.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.option-radio {
    display: none;
}

.option-content {
    text-align: center;
}

.option-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: block;
}

.option-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.option-description {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* ===== PROGRESS ===== */
.progress-container {
    margin: 30px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.progress-text {
    font-weight: 600;
    color: var(--primary-color);
}

.progress-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-align: center;
}

/* ===== RESULTS ===== */
.results-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
}

.result-item {
    text-align: center;
}

.result-image {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.result-image:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

.result-label {
    font-weight: 600;
    margin: 15px 0 5px;
    color: var(--text-primary);
}

.result-size {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* ===== MODAL ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-card);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
}

.modal-header {
    text-align: center;
    margin-bottom: 20px;
}

.modal-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* ===== NOTIFICATIONS ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: slideIn 0.3s ease;
    max-width: 400px;
    box-shadow: var(--shadow-lg);
}

.notification-success {
    background: linear-gradient(135deg, var(--success-color), #38a169);
}

.notification-error {
    background: linear-gradient(135deg, var(--error-color), #c53030);
}

.notification-info {
    background: linear-gradient(135deg, var(--info-color), #3182ce);
}

.notification-warning {
    background: linear-gradient(135deg, var(--warning-color), #dd6b20);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== UTILITIES ===== */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .card {
        padding: 20px;
    }
    
    .upload-area {
        padding: 40px 20px;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .enhancement-options {
        grid-template-columns: 1fr;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .modal-content {
        width: 95%;
        padding: 20px;
    }
    
    .modal-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}
