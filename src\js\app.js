// Main Application Module - Fixed and Reorganized
// التطبيق الرئيسي - مُصحح ومُعاد تنظيمه

class ImageEnhancerApp {
    constructor() {
        this.currentFile = null;
        this.enhancedImage = null;
        this.originalImageBase64 = null;
        this.originalDimensions = null;
        this.isProcessing = false;
        this.apiKey = null;
        
        // تهيئة المعالجات
        this.imageProcessor = new ImageProcessor();
        this.geminiAPI = null;
        
        this.init();
    }

    // تهيئة التطبيق
    async init() {
        // التحقق من دعم المتصفح
        if (!Utils.checkBrowserSupport()) {
            return;
        }

        // تحميل مفتاح API
        this.loadApiKey();
        
        // إعداد مستمعي الأحداث
        this.setupEventListeners();
        
        // إعداد السحب والإفلات
        this.setupDragAndDrop();
        
        console.log('✅ تم تهيئة التطبيق بنجاح');
    }

    // تحميل مفتاح API
    loadApiKey() {
        this.apiKey = Utils.getApiKey();
        
        if (this.apiKey && Utils.isApiKeyConfigured()) {
            this.geminiAPI = new GeminiAPI(this.apiKey);
            console.log('✅ تم تحميل مفتاح API من متغيرات البيئة');
        } else {
            console.log('⚠️ لم يتم العثور على مفتاح API');
        }
    }

    // حفظ مفتاح API
    saveApiKey(key) {
        this.apiKey = key;
        this.geminiAPI = new GeminiAPI(key);
        Utils.saveToStorage('gemini_api_key', key);
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // رفع الملف
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }

        // أزرار التحسين
        const enhanceBtn = document.getElementById('enhanceBtn');
        if (enhanceBtn) {
            enhanceBtn.addEventListener('click', () => this.enhanceImage());
        }

        // أزرار أخرى
        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadImage());
        }

        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetApp());
        }

        const removeBtn = document.getElementById('removeBtn');
        if (removeBtn) {
            removeBtn.addEventListener('click', () => this.removeFile());
        }

        // modal مفتاح API
        const saveApiKeyBtn = document.getElementById('saveApiKey');
        if (saveApiKeyBtn) {
            saveApiKeyBtn.addEventListener('click', () => this.handleApiKeySave());
        }

        const cancelApiKeyBtn = document.getElementById('cancelApiKey');
        if (cancelApiKeyBtn) {
            cancelApiKeyBtn.addEventListener('click', () => this.hideApiKeyModal());
        }

        // Enter key في input مفتاح API
        const apiKeyInput = document.getElementById('apiKeyInput');
        if (apiKeyInput) {
            apiKeyInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleApiKeySave();
                }
            });
        }
    }

    // إعداد السحب والإفلات
    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFile(files[0]);
            }
        });

        uploadArea.addEventListener('click', () => {
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.click();
            }
        });
    }

    // معالجة اختيار الملف
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.handleFile(file);
        }
    }

    // معالجة الملف
    async handleFile(file) {
        // التحقق من صحة الملف
        const errors = Utils.validateImageFile(file);
        if (errors.length > 0) {
            Utils.showNotification(errors[0], 'error');
            return;
        }

        this.currentFile = file;

        try {
            // عرض معلومات الملف
            this.showFileInfo(file);

            // عرض قسم الخيارات
            this.showSection('optionsSection');

            // تحميل وحفظ بيانات الصورة
            this.originalImageBase64 = await Utils.fileToBase64(file);
            this.originalDimensions = await Utils.getImageDimensions(file);

            Utils.showNotification('تم رفع الصورة بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في معالجة الملف:', error);
            Utils.showNotification('فشل في معالجة الملف', 'error');
        }
    }

    // عرض معلومات الملف
    showFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');

        if (fileName) fileName.textContent = file.name;
        if (fileSize) fileSize.textContent = Utils.formatFileSize(file.size);
        if (fileInfo) fileInfo.style.display = 'flex';
    }

    // تحسين الصورة
    async enhanceImage() {
        if (!this.currentFile) {
            Utils.showNotification('يرجى اختيار صورة أولاً', 'error');
            return;
        }

        if (!this.apiKey && !Utils.isApiKeyConfigured()) {
            this.showApiKeyModal();
            return;
        }

        if (this.isProcessing) {
            return;
        }

        this.isProcessing = true;

        try {
            // عرض قسم التقدم
            this.showSection('progressSection');
            this.hideSection('optionsSection');
            this.hideSection('errorSection');
            this.hideSection('resultsSection');

            // الحصول على خيار التحسين المحدد
            const selectedOption = document.querySelector('input[name="enhancement"]:checked')?.value || 'auto';

            // بدء محاكاة التقدم
            this.simulateProgress();

            let enhancedData;

            // محاولة استخدام Gemini AI أولاً
            if (this.geminiAPI) {
                try {
                    Utils.showNotification('جاري التحليل باستخدام Gemini AI...', 'info');
                    const analysisResult = await this.geminiAPI.analyzeImage(this.originalImageBase64, selectedOption);
                    
                    Utils.showNotification('جاري تطبيق التحسينات...', 'info');
                    enhancedData = await this.imageProcessor.enhanceImage(this.originalImageBase64, {
                        enhancementType: selectedOption,
                        aiInstructions: analysisResult
                    });
                    
                } catch (geminiError) {
                    console.error('Gemini API Error:', geminiError);
                    Utils.showNotification('فشل في استخدام Gemini AI، جاري التحسين المحلي...', 'info');
                    
                    // العودة للتحسين المحلي
                    enhancedData = await this.imageProcessor.enhanceImage(this.originalImageBase64, {
                        enhancementType: selectedOption
                    });
                }
            } else {
                // تحسين محلي مباشر
                Utils.showNotification('جاري التحسين المحلي...', 'info');
                enhancedData = await this.imageProcessor.enhanceImage(this.originalImageBase64, {
                    enhancementType: selectedOption
                });
            }

            // عرض النتائج
            await this.showResults(enhancedData);

        } catch (error) {
            console.error('خطأ في التحسين:', error);
            this.showError(error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    // محاكاة شريط التقدم
    simulateProgress() {
        const progressSteps = [
            { progress: 10, message: 'تحليل الصورة...' },
            { progress: 30, message: 'تطبيق خوارزميات التحسين...' },
            { progress: 60, message: 'تحسين الجودة...' },
            { progress: 85, message: 'إنهاء المعالجة...' },
            { progress: 100, message: 'تم الانتهاء!' }
        ];

        let currentStep = 0;
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const progressMessage = document.getElementById('progressMessage');

        const updateProgress = () => {
            if (currentStep < progressSteps.length) {
                const step = progressSteps[currentStep];
                
                if (progressFill) progressFill.style.width = step.progress + '%';
                if (progressText) progressText.textContent = step.progress + '%';
                if (progressMessage) progressMessage.textContent = step.message;

                currentStep++;

                if (currentStep < progressSteps.length) {
                    setTimeout(updateProgress, 800 + Math.random() * 1200);
                }
            }
        };

        updateProgress();
    }

    // عرض النتائج
    async showResults(enhancedData) {
        // إخفاء قسم التقدم
        this.hideSection('progressSection');

        // عرض قسم النتائج
        this.showSection('resultsSection');

        // عرض الصورة الأصلية
        const originalImage = document.getElementById('originalImage');
        if (originalImage) {
            originalImage.src = this.originalImageBase64;
        }

        // عرض الصورة المحسنة
        const enhancedImage = document.getElementById('enhancedImage');
        if (enhancedImage) {
            enhancedImage.src = enhancedData.enhancedImage;
        }
        
        this.enhancedImage = enhancedData.enhancedImage;

        // تحديث معلومات الحجم
        const originalSize = document.getElementById('originalSize');
        const enhancedSize = document.getElementById('enhancedSize');

        if (originalSize && this.originalDimensions) {
            originalSize.textContent = `${this.originalDimensions.width} × ${this.originalDimensions.height}`;
        }
        
        if (enhancedSize && enhancedData.enhancedSize) {
            enhancedSize.textContent = `${enhancedData.enhancedSize.width} × ${enhancedData.enhancedSize.height}`;
        }

        Utils.showNotification('تم تحسين الصورة بنجاح!', 'success');
    }

    // عرض الخطأ
    showError(message) {
        this.hideSection('progressSection');
        this.showSection('errorSection');

        const errorMessage = document.getElementById('errorMessage');
        if (errorMessage) {
            errorMessage.textContent = message || 'حدث خطأ غير متوقع';
        }
    }

    // تحميل الصورة المحسنة
    downloadImage() {
        if (!this.enhancedImage) {
            Utils.showNotification('لا توجد صورة محسنة للتحميل', 'error');
            return;
        }

        const filename = `enhanced_${this.currentFile.name}`;
        Utils.downloadFile(this.enhancedImage, filename);
        Utils.showNotification('تم تحميل الصورة بنجاح', 'success');
    }

    // إزالة الملف
    removeFile() {
        this.currentFile = null;
        this.enhancedImage = null;
        this.originalImageBase64 = null;
        this.originalDimensions = null;

        // إخفاء معلومات الملف
        const fileInfo = document.getElementById('fileInfo');
        if (fileInfo) fileInfo.style.display = 'none';

        // إعادة تعيين input الملف
        const fileInput = document.getElementById('fileInput');
        if (fileInput) fileInput.value = '';

        // إخفاء الأقسام
        this.hideSection('optionsSection');
        this.hideSection('resultsSection');
        this.hideSection('errorSection');
        this.hideSection('progressSection');
    }

    // إعادة تعيين التطبيق
    resetApp() {
        this.removeFile();
        this.isProcessing = false;
        this.imageProcessor.cleanup();
        Utils.cleanup();
    }

    // عرض/إخفاء الأقسام
    showSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'block';
        }
    }

    hideSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'none';
        }
    }

    // modal مفتاح API
    showApiKeyModal() {
        const modal = document.getElementById('apiKeyModal');
        if (modal) modal.style.display = 'block';
    }

    hideApiKeyModal() {
        const modal = document.getElementById('apiKeyModal');
        if (modal) modal.style.display = 'none';
    }

    handleApiKeySave() {
        const apiKeyInput = document.getElementById('apiKeyInput');
        const apiKey = apiKeyInput?.value.trim();

        if (!apiKey) {
            Utils.showNotification('يرجى إدخال مفتاح API', 'error');
            return;
        }

        this.saveApiKey(apiKey);
        this.hideApiKeyModal();
        if (apiKeyInput) apiKeyInput.value = '';

        Utils.showNotification('تم حفظ مفتاح API بنجاح', 'success');
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.imageEnhancerApp = new ImageEnhancerApp();
});

// تصدير الفئة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageEnhancerApp;
} else {
    window.ImageEnhancerApp = ImageEnhancerApp;
}
