#!/usr/bin/env python3
"""
خادم محلي بسيط لتطبيق محسن جودة الصور
Simple local server for Gemini Image Enhancer
"""

import http.server
import socketserver
import os
import webbrowser
import threading
import time
from urllib.parse import urlparse, parse_qs
import json
import base64
import mimetypes

class ImageEnhancerHandler(http.server.SimpleHTTPRequestHandler):
    """معالج مخصص للطلبات"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path == '/':
            self.path = '/index.html'
        
        # إضافة headers للأمان
        self.add_security_headers()
        
        return super().do_GET()
    
    def do_POST(self):
        """معالجة طلبات POST للAPI"""
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(404, "API endpoint not found")
    
    def handle_api_request(self):
        """معالجة طلبات API"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            if self.path == '/api/enhance-image':
                self.handle_image_enhancement(post_data)
            else:
                self.send_error(404, "API endpoint not found")
                
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")
    
    def handle_image_enhancement(self, post_data):
        """معالجة طلب تحسين الصورة"""
        # هذا مثال تجريبي - في التطبيق الحقيقي ستحتاج لاستخدام Gemini API
        response_data = {
            "success": True,
            "message": "تم تحسين الصورة بنجاح (تجريبي)",
            "enhanced_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
            "metadata": {
                "original_size": {"width": 800, "height": 600},
                "enhanced_size": {"width": 1600, "height": 1200},
                "enhancement_type": "2x",
                "processing_time": 2.5
            }
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.add_cors_headers()
        self.end_headers()
        
        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
    
    def add_security_headers(self):
        """إضافة headers للأمان"""
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
    
    def add_cors_headers(self):
        """إضافة CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
    
    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.add_cors_headers()
        self.end_headers()
    
    def log_message(self, format, *args):
        """تخصيص رسائل السجل"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def find_free_port(start_port=8000, max_attempts=10):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socketserver.TCPServer(("", port), None) as s:
                return port
        except OSError:
            continue
    return None

def open_browser(url, delay=1.5):
    """فتح المتصفح بعد تأخير"""
    time.sleep(delay)
    webbrowser.open(url)

def print_banner():
    """طباعة شعار التطبيق"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🎨 محسن جودة الصور بالذكاء الاصطناعي - جيميني        ║
    ║                                                              ║
    ║                Gemini AI Image Enhancer                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_info(port, host="localhost"):
    """طباعة معلومات الخادم"""
    url = f"http://{host}:{port}"
    
    print(f"""
    🚀 الخادم يعمل على: {url}
    📁 المجلد الحالي: {os.getcwd()}
    
    📋 الأوامر المتاحة:
    • Ctrl+C : إيقاف الخادم
    • المتصفح سيفتح تلقائياً خلال ثوانٍ...
    
    🔗 الروابط المفيدة:
    • التطبيق: {url}
    • API التجريبي: {url}/api/enhance-image
    
    ⚠️  ملاحظة: هذا خادم تطوير محلي فقط
    """)

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # البحث عن منفذ متاح
    port = find_free_port()
    if port is None:
        print("❌ خطأ: لا يمكن العثور على منفذ متاح")
        return
    
    # إعداد الخادم
    handler = ImageEnhancerHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print_info(port)
            
            # فتح المتصفح في خيط منفصل
            browser_thread = threading.Thread(
                target=open_browser, 
                args=(f"http://localhost:{port}",)
            )
            browser_thread.daemon = True
            browser_thread.start()
            
            # تشغيل الخادم
            print("🟢 الخادم جاهز! اضغط Ctrl+C للإيقاف\n")
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الخادم: {e}")
    finally:
        print("👋 شكراً لاستخدام محسن جودة الصور!")

if __name__ == "__main__":
    main()
