// Gemini AI Image Enhancer - Main Application Logic

class ImageEnhancer {
    constructor() {
        this.apiKey = null;
        this.currentFile = null;
        this.enhancedImage = null;
        this.isProcessing = false;

        this.init();
    }

    init() {
        this.loadApiKey();
        this.setupEventListeners();
        this.setupDragAndDrop();

        // Show API key modal if not set
        if (!this.apiKey) {
            this.showApiKeyModal();
        }
    }

    loadApiKey() {
        // Try to get API key from environment first, then localStorage
        this.apiKey = Utils.getApiKey();

        // Show success message if API key is loaded from environment
        if (this.apiKey && Utils.isApiKeyConfigured()) {
            console.log('✅ API key loaded from environment variables');
        }
    }

    saveApiKey(key) {
        this.apiKey = key;
        Utils.saveToStorage(CONFIG.STORAGE_KEYS.API_KEY, key);
    }

    setupEventListeners() {
        // File input
        const fileInput = document.getElementById('fileInput');
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

        // Remove file button
        const removeBtn = document.getElementById('removeFile');
        removeBtn.addEventListener('click', () => this.removeFile());

        // Enhancement button
        const enhanceBtn = document.getElementById('enhanceBtn');
        enhanceBtn.addEventListener('click', () => this.enhanceImage());

        // Download button
        const downloadBtn = document.getElementById('downloadBtn');
        downloadBtn.addEventListener('click', () => this.downloadImage());

        // New image button
        const newImageBtn = document.getElementById('newImageBtn');
        newImageBtn.addEventListener('click', () => this.resetApp());

        // Retry button
        const retryBtn = document.getElementById('retryBtn');
        retryBtn.addEventListener('click', () => this.enhanceImage());

        // API Key modal
        const saveApiKeyBtn = document.getElementById('saveApiKey');
        saveApiKeyBtn.addEventListener('click', () => this.handleApiKeySave());

        const cancelApiKeyBtn = document.getElementById('cancelApiKey');
        cancelApiKeyBtn.addEventListener('click', () => this.hideApiKeyModal());

        // API Key input enter key
        const apiKeyInput = document.getElementById('apiKeyInput');
        apiKeyInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleApiKeySave();
            }
        });
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFile(files[0]);
            }
        });

        uploadArea.addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.handleFile(file);
        }
    }

    async handleFile(file) {
        // Validate file
        const errors = Utils.validateImageFile(file);
        if (errors.length > 0) {
            Utils.showNotification(errors[0], 'error');
            return;
        }

        this.currentFile = file;

        // Show file info
        this.showFileInfo(file);

        // Show options section
        this.showSection('optionsSection');

        // Get image dimensions
        const dimensions = await Utils.getImageDimensions(file);
        console.log('Image dimensions:', dimensions);

        Utils.showNotification(CONFIG.MESSAGES.UPLOAD.UPLOAD_SUCCESS, 'success');
    }

    showFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');

        fileName.textContent = file.name;
        fileSize.textContent = Utils.formatFileSize(file.size);

        fileInfo.style.display = 'flex';
    }

    removeFile() {
        this.currentFile = null;
        this.enhancedImage = null;

        // Clear cached data to prevent memory leaks
        this.originalImageBase64 = null;
        this.originalDimensions = null;

        // Hide file info
        document.getElementById('fileInfo').style.display = 'none';

        // Reset file input
        document.getElementById('fileInput').value = '';

        // Hide sections
        this.hideSection('optionsSection');
        this.hideSection('resultsSection');
        this.hideSection('errorSection');
        this.hideSection('progressSection');
    }

    async enhanceImage() {
        if (!this.currentFile) {
            Utils.showNotification('يرجى اختيار صورة أولاً', 'error');
            return;
        }

        if (!this.apiKey) {
            this.showApiKeyModal();
            return;
        }

        if (this.isProcessing) {
            return;
        }

        this.isProcessing = true;

        try {
            // Show progress section
            this.showSection('progressSection');
            this.hideSection('optionsSection');
            this.hideSection('errorSection');
            this.hideSection('resultsSection');

            // Get selected enhancement option
            const selectedOption = document.querySelector('input[name="enhancement"]:checked').value;

            // Start progress simulation
            this.simulateProgress();

            // Convert image to base64 once and store it
            if (!this.originalImageBase64) {
                this.originalImageBase64 = await Utils.fileToBase64(this.currentFile);
            }

            // Get original dimensions once and store them
            if (!this.originalDimensions) {
                this.originalDimensions = await Utils.getImageDimensions(this.currentFile);
            }

            // Call Gemini API for image enhancement
            const enhancedImageData = await this.callGeminiAPI(this.originalImageBase64, selectedOption);

            // Show results
            await this.showResults(enhancedImageData);

        } catch (error) {
            console.error('Enhancement error:', error);
            this.showError(error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    simulateProgress() {
        let currentStep = 0;
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        const updateProgress = () => {
            if (currentStep < CONFIG.PROGRESS_STEPS.length) {
                const step = CONFIG.PROGRESS_STEPS[currentStep];
                progressFill.style.width = step.progress + '%';
                progressText.textContent = step.progress + '%';

                // Update message if available
                const progressMessage = document.getElementById('progressMessage');
                if (progressMessage) {
                    progressMessage.textContent = step.message;
                }

                currentStep++;

                if (currentStep < CONFIG.PROGRESS_STEPS.length) {
                    setTimeout(updateProgress, 1000 + Math.random() * 2000);
                }
            }
        };

        updateProgress();
    }

    async callGeminiAPI(base64Image, enhancementOption) {
        try {
            // محاولة استخدام Gemini API الحقيقي أولاً
            Utils.showNotification('جاري الاتصال بـ Gemini AI...', 'info');
            const enhancedImageData = await this.enhanceImageWithGeminiAPI(base64Image, enhancementOption);
            return enhancedImageData;
        } catch (error) {
            console.error('Gemini API Error:', error);

            try {
                // محاولة استخدام الخادم الخلفي كبديل
                Utils.showNotification('جاري المحاولة مع الخادم الخلفي...', 'info');
                const enhancedImageData = await this.enhanceImageWithBackend(enhancementOption);
                return enhancedImageData;
            } catch (backendError) {
                console.error('Backend API Error:', backendError);
                Utils.showNotification('جاري استخدام التحسين المحلي المتقدم...', 'info');
                // في حالة فشل كل شيء، نستخدم تحسين محلي متقدم
                return await this.enhanceImageLocally(base64Image, enhancementOption);
            }
        }
    }

    async enhanceImageWithBackend(enhancementOption) {
        const formData = new FormData();
        formData.append('image', this.currentFile);
        formData.append('enhancementType', enhancementOption);
        formData.append('apiKey', this.apiKey);

        const response = await fetch('/api/enhance-image', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'خطأ في الخادم الخلفي');
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error || 'فشل في تحسين الصورة');
        }

        return {
            enhancedImage: result.enhanced_image,
            originalSize: result.original_size,
            enhancedSize: result.enhanced_size,
            enhancementFactor: result.enhancement_type
        };
    }

    async enhanceImageWithGeminiAPI(base64Image, enhancementOption) {
        if (!this.apiKey) {
            throw new Error(CONFIG.MESSAGES.ENHANCEMENT.NO_API_KEY);
        }

        try {
            // تحضير البيانات للإرسال
            const imageData = base64Image.split(',')[1]; // إزالة البادئة data:image/...;base64,
            const mimeType = base64Image.split(';')[0].split(':')[1]; // استخراج نوع الصورة

            const prompt = this.generateEnhancementPrompt(enhancementOption);

            const requestBody = {
                contents: [{
                    parts: [
                        {
                            text: prompt
                        },
                        {
                            inline_data: {
                                mime_type: mimeType,
                                data: imageData
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.4,
                    topK: 32,
                    topP: 1,
                    maxOutputTokens: 4096,
                }
            };

            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Gemini API Error: ${response.status} - ${errorData.error?.message || response.statusText}`);
            }

            const result = await response.json();

            if (result.candidates && result.candidates.length > 0) {
                const candidate = result.candidates[0];
                const responseText = candidate.content.parts[0].text;

                // ملاحظة: Gemini حالياً لا يدعم إنتاج صور محسنة مباشرة
                // لذا سنستخدم الاستجابة لتوجيه التحسين المحلي
                const enhancementInstructions = this.parseEnhancementInstructions(responseText);
                return await this.enhanceImageLocallyWithAI(base64Image, enhancementOption, enhancementInstructions);

            } else {
                throw new Error('No response from Gemini API');
            }

        } catch (error) {
            console.error('Gemini API Error:', error);
            // في حالة فشل Gemini API، نستخدم التحسين المحلي
            throw error;
        }
    }

    parseEnhancementInstructions(responseText) {
        // تحليل استجابة Gemini لاستخراج تعليمات التحسين
        const instructions = {
            sharpness: 1.3,
            contrast: 1.1,
            brightness: 1.0,
            saturation: 1.05,
            noiseReduction: true,
            detailEnhancement: true
        };

        const text = responseText.toLowerCase();

        // تحليل قيم الحدة
        const sharpnessMatch = text.match(/sharpness[:\s]*([0-9.]+)/);
        if (sharpnessMatch) {
            instructions.sharpness = Math.min(2.0, Math.max(1.0, parseFloat(sharpnessMatch[1])));
        } else if (text.includes('very sharp') || text.includes('high sharpness')) {
            instructions.sharpness = 1.8;
        } else if (text.includes('sharp') || text.includes('crisp')) {
            instructions.sharpness = 1.5;
        } else if (text.includes('soft') || text.includes('blur')) {
            instructions.sharpness = 1.2;
        }

        // تحليل قيم التباين
        const contrastMatch = text.match(/contrast[:\s]*([0-9.]+)/);
        if (contrastMatch) {
            instructions.contrast = Math.min(1.5, Math.max(0.8, parseFloat(contrastMatch[1])));
        } else if (text.includes('high contrast') || text.includes('strong contrast')) {
            instructions.contrast = 1.3;
        } else if (text.includes('low contrast') || text.includes('flat')) {
            instructions.contrast = 1.2;
        }

        // تحليل قيم السطوع
        const brightnessMatch = text.match(/brightness[:\s]*([0-9.]+)/);
        if (brightnessMatch) {
            instructions.brightness = Math.min(1.2, Math.max(0.8, parseFloat(brightnessMatch[1])));
        } else if (text.includes('bright') || text.includes('light')) {
            instructions.brightness = 1.1;
        } else if (text.includes('dark') || text.includes('dim')) {
            instructions.brightness = 1.15;
        }

        // تحليل قيم التشبع
        const saturationMatch = text.match(/saturation[:\s]*([0-9.]+)/);
        if (saturationMatch) {
            instructions.saturation = Math.min(1.3, Math.max(0.8, parseFloat(saturationMatch[1])));
        } else if (text.includes('vibrant') || text.includes('colorful')) {
            instructions.saturation = 1.2;
        } else if (text.includes('muted') || text.includes('desaturated')) {
            instructions.saturation = 0.9;
        }

        // تحليل الحاجة لتقليل الضوضاء
        if (text.includes('noise') || text.includes('grain') || text.includes('artifact')) {
            instructions.noiseReduction = true;
        } else if (text.includes('clean') || text.includes('smooth')) {
            instructions.noiseReduction = false;
        }

        // تحليل الحاجة لتحسين التفاصيل
        if (text.includes('detail') || text.includes('texture') || text.includes('fine')) {
            instructions.detailEnhancement = true;
        }

        console.log('Gemini AI Enhancement Instructions:', instructions);
        return instructions;
    }

    async enhanceImageLocallyWithAI(base64Image, enhancementOption, instructions) {
        // تحسين محلي موجه بالذكاء الاصطناعي
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // تحديد حجم الصورة المحسنة
                const scaleFactor = enhancementOption === '4x' ? 4 : enhancementOption === '2x' ? 2 : 2;
                canvas.width = img.width * scaleFactor;
                canvas.height = img.height * scaleFactor;

                // تطبيق تحسينات محلية موجهة بالذكاء الاصطناعي
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                // رسم الصورة بحجم أكبر
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                // تطبيق فلاتر تحسين موجهة بالذكاء الاصطناعي
                this.applyAIGuidedFilters(ctx, canvas.width, canvas.height, instructions);

                const enhancedImage = canvas.toDataURL('image/png', 1.0);

                resolve({
                    enhancedImage: enhancedImage,
                    originalSize: { width: img.width, height: img.height },
                    enhancedSize: { width: canvas.width, height: canvas.height },
                    enhancementFactor: enhancementOption
                });
            };
            img.src = base64Image;
        });
    }

    applyAIGuidedFilters(ctx, width, height, instructions) {
        // الحصول على بيانات الصورة
        const imageData = ctx.getImageData(0, 0, width, height);
        const data = imageData.data;

        // تطبيق فلاتر موجهة بالذكاء الاصطناعي
        if (instructions.sharpness > 1) {
            this.applySharpenFilter(data, width, height, instructions.sharpness);
        }

        if (instructions.contrast !== 1) {
            this.applyContrastFilter(data, instructions.contrast);
        }

        if (instructions.brightness !== 1) {
            this.applyBrightnessFilter(data, instructions.brightness);
        }

        if (instructions.saturation !== 1) {
            this.applySaturationFilter(data, instructions.saturation);
        }

        if (instructions.noiseReduction) {
            this.applyAdvancedNoiseReduction(data, width, height);
        }

        if (instructions.detailEnhancement) {
            this.applyDetailEnhancement(data, width, height);
        }

        // إعادة رسم الصورة المحسنة
        ctx.putImageData(imageData, 0, 0);
    }

    async enhanceImageLocally(base64Image, enhancementOption) {
        // تحسين محلي باستخدام Canvas API
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // تحديد حجم الصورة المحسنة
                const scaleFactor = enhancementOption === '4x' ? 4 : enhancementOption === '2x' ? 2 : 2;
                canvas.width = img.width * scaleFactor;
                canvas.height = img.height * scaleFactor;

                // تطبيق تحسينات محلية
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                // رسم الصورة بحجم أكبر
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                // تطبيق فلاتر تحسين
                this.applyImageFilters(ctx, canvas.width, canvas.height);

                const enhancedImage = canvas.toDataURL('image/png', 1.0);

                resolve({
                    enhancedImage: enhancedImage,
                    originalSize: { width: img.width, height: img.height },
                    enhancedSize: { width: canvas.width, height: canvas.height },
                    enhancementFactor: enhancementOption
                });
            };
            img.src = base64Image;
        });
    }

    applyImageFilters(ctx, width, height) {
        // الحصول على بيانات الصورة
        const imageData = ctx.getImageData(0, 0, width, height);
        const data = imageData.data;

        // تطبيق فلتر تحسين الحدة (Sharpening)
        this.applySharpenFilter(data, width, height, 1.3);

        // تطبيق فلتر تحسين التباين
        this.applyContrastFilter(data, 1.2);

        // تطبيق فلتر تقليل الضوضاء
        this.applyNoiseReduction(data, width, height);

        // إعادة رسم الصورة المحسنة
        ctx.putImageData(imageData, 0, 0);
    }

    applySharpenFilter(data, width, height, intensity = 1.3) {
        const sharpenKernel = [
            0, -1, 0,
            -1, 5, -1,
            0, -1, 0
        ];

        this.applyConvolutionFilter(data, width, height, sharpenKernel, Math.min(intensity, 2.0));
    }

    applyContrastFilter(data, contrast = 1.2) {
        const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255));

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, Math.max(0, factor * (data[i] - 128) + 128));     // Red
            data[i + 1] = Math.min(255, Math.max(0, factor * (data[i + 1] - 128) + 128)); // Green
            data[i + 2] = Math.min(255, Math.max(0, factor * (data[i + 2] - 128) + 128)); // Blue
        }
    }

    applyBrightnessFilter(data, brightness = 1.1) {
        const adjustment = (brightness - 1) * 255;

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, Math.max(0, data[i] + adjustment));     // Red
            data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + adjustment)); // Green
            data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + adjustment)); // Blue
        }
    }

    applySaturationFilter(data, saturation = 1.05) {
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            // تحويل إلى grayscale
            const gray = 0.299 * r + 0.587 * g + 0.114 * b;

            // تطبيق التشبع
            data[i] = Math.min(255, Math.max(0, gray + saturation * (r - gray)));
            data[i + 1] = Math.min(255, Math.max(0, gray + saturation * (g - gray)));
            data[i + 2] = Math.min(255, Math.max(0, gray + saturation * (b - gray)));
        }
    }

    applyAdvancedNoiseReduction(data, width, height) {
        // فلتر متقدم لتقليل الضوضاء باستخدام Bilateral Filter
        const output = new Uint8ClampedArray(data);
        const radius = 2;
        const sigmaColor = 50;
        const sigmaSpace = 50;

        for (let y = radius; y < height - radius; y++) {
            for (let x = radius; x < width - radius; x++) {
                for (let c = 0; c < 3; c++) {
                    let weightSum = 0;
                    let valueSum = 0;
                    const centerIdx = (y * width + x) * 4 + c;
                    const centerValue = data[centerIdx];

                    for (let dy = -radius; dy <= radius; dy++) {
                        for (let dx = -radius; dx <= radius; dx++) {
                            const neighborIdx = ((y + dy) * width + (x + dx)) * 4 + c;
                            const neighborValue = data[neighborIdx];

                            const spatialWeight = Math.exp(-(dx * dx + dy * dy) / (2 * sigmaSpace * sigmaSpace));
                            const colorWeight = Math.exp(-Math.pow(centerValue - neighborValue, 2) / (2 * sigmaColor * sigmaColor));
                            const weight = spatialWeight * colorWeight;

                            weightSum += weight;
                            valueSum += weight * neighborValue;
                        }
                    }

                    output[centerIdx] = valueSum / weightSum;
                }
            }
        }

        // نسخ النتيجة
        for (let i = 0; i < data.length; i++) {
            data[i] = output[i];
        }
    }

    applyDetailEnhancement(data, width, height) {
        // فلتر تحسين التفاصيل باستخدام Unsharp Mask
        const blurred = new Uint8ClampedArray(data);

        // تطبيق Gaussian Blur أولاً
        const blurKernel = [
            1/16, 2/16, 1/16,
            2/16, 4/16, 2/16,
            1/16, 2/16, 1/16
        ];

        this.applyConvolutionFilter(blurred, width, height, blurKernel, 1);

        // تطبيق Unsharp Mask
        const amount = 0.5;
        for (let i = 0; i < data.length; i += 4) {
            for (let c = 0; c < 3; c++) {
                const original = data[i + c];
                const blur = blurred[i + c];
                const enhanced = original + amount * (original - blur);
                data[i + c] = Math.min(255, Math.max(0, enhanced));
            }
        }
    }

    applyNoiseReduction(data, width, height) {
        // فلتر بسيط لتقليل الضوضاء
        const blurKernel = [
            1/16, 2/16, 1/16,
            2/16, 4/16, 2/16,
            1/16, 2/16, 1/16
        ];

        this.applyConvolutionFilter(data, width, height, blurKernel, 0.3);
    }

    applyConvolutionFilter(data, width, height, kernel, intensity = 1) {
        const output = new Uint8ClampedArray(data);

        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                for (let c = 0; c < 3; c++) { // RGB channels only
                    let sum = 0;
                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const idx = ((y + ky) * width + (x + kx)) * 4 + c;
                            const kernelIdx = (ky + 1) * 3 + (kx + 1);
                            sum += data[idx] * kernel[kernelIdx];
                        }
                    }

                    const currentIdx = (y * width + x) * 4 + c;
                    output[currentIdx] = Math.min(255, Math.max(0,
                        data[currentIdx] * (1 - intensity) + sum * intensity
                    ));
                }
            }
        }

        // نسخ النتيجة إلى البيانات الأصلية
        for (let i = 0; i < data.length; i++) {
            data[i] = output[i];
        }
    }

    async getImageDimensions(imageSrc) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                resolve({ width: img.width, height: img.height });
            };
            img.src = imageSrc;
        });
    }

    generateEnhancementPrompt(enhancementOption) {
        const prompts = {
            '2x': `Analyze this image and provide detailed enhancement recommendations for 2x upscaling.
                   Focus on: sharpness improvement, contrast enhancement, noise reduction, and detail preservation.
                   Suggest specific values for sharpness (1.0-2.0), contrast (0.8-1.5), brightness (0.8-1.2),
                   and saturation (0.8-1.3). Also indicate if the image needs noise reduction or detail enhancement.`,

            '4x': `Analyze this image for 4x upscaling enhancement. Provide comprehensive recommendations for:
                   maximum detail preservation, advanced noise reduction, edge sharpening, color enhancement,
                   and artifact removal. Suggest optimal values for all enhancement parameters and identify
                   specific areas that need attention (faces, text, textures, edges).`,

            'auto': `Perform intelligent analysis of this image to determine the best enhancement strategy.
                     Evaluate image quality, identify issues (blur, noise, low contrast, poor lighting),
                     and recommend the most suitable enhancement approach. Provide specific parameter values
                     and explain the reasoning for each enhancement decision.`
        };

        return prompts[enhancementOption] || prompts['auto'];
    }

    async showResults(enhancedData) {
        // Hide progress section
        this.hideSection('progressSection');

        // Show results section
        this.showSection('resultsSection');

        // Display original image using cached data
        const originalImage = document.getElementById('originalImage');
        originalImage.src = this.originalImageBase64;

        // Display enhanced image
        const enhancedImage = document.getElementById('enhancedImage');
        enhancedImage.src = enhancedData.enhancedImage;
        this.enhancedImage = enhancedData.enhancedImage;

        // Update size information using cached dimensions
        const originalSize = document.getElementById('originalSize');
        const enhancedSize = document.getElementById('enhancedSize');

        originalSize.textContent = `${this.originalDimensions.width} × ${this.originalDimensions.height}`;
        enhancedSize.textContent = `${enhancedData.enhancedSize.width} × ${enhancedData.enhancedSize.height}`;

        Utils.showNotification(CONFIG.MESSAGES.ENHANCEMENT.SUCCESS, 'success');
    }

    showError(message) {
        this.hideSection('progressSection');
        this.showSection('errorSection');

        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message || CONFIG.MESSAGES.ERRORS.API_ERROR;
    }

    downloadImage() {
        if (!this.enhancedImage) {
            Utils.showNotification('لا توجد صورة محسنة للتحميل', 'error');
            return;
        }

        // Create download link
        const link = document.createElement('a');
        link.href = this.enhancedImage;
        link.download = `enhanced_${this.currentFile.name}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        Utils.showNotification('تم تحميل الصورة بنجاح', 'success');
    }

    resetApp() {
        this.removeFile();
        this.enhancedImage = null;
        this.isProcessing = false;

        // Clear all cached data
        this.originalImageBase64 = null;
        this.originalDimensions = null;
    }

    showSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'block';
        }
    }

    hideSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'none';
        }
    }

    showApiKeyModal() {
        const modal = document.getElementById('apiKeyModal');
        modal.style.display = 'block';
    }

    hideApiKeyModal() {
        const modal = document.getElementById('apiKeyModal');
        modal.style.display = 'none';
    }

    handleApiKeySave() {
        const apiKeyInput = document.getElementById('apiKeyInput');
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            Utils.showNotification('يرجى إدخال مفتاح API', 'error');
            return;
        }

        this.saveApiKey(apiKey);
        this.hideApiKeyModal();
        apiKeyInput.value = '';

        Utils.showNotification('تم حفظ مفتاح API بنجاح', 'success');
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ImageEnhancer();
});

// Add notification styles dynamically
const notificationStyles = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 10px;
        color: white;
        font-weight: 600;
        z-index: 1001;
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.3s ease;
        max-width: 400px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        font-family: 'Cairo', sans-serif;
    }

    .notification-success {
        background: linear-gradient(135deg, #48bb78, #38a169);
    }

    .notification-error {
        background: linear-gradient(135deg, #e53e3e, #c53030);
    }

    .notification-info {
        background: linear-gradient(135deg, #667eea, #764ba2);
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @media (max-width: 768px) {
        .notification {
            right: 10px;
            left: 10px;
            max-width: none;
        }
    }
`;

// Add styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);

// Enhanced Gemini API Integration (Backend Required)
class GeminiAPIService {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
    }

    async enhanceImage(imageData, options = {}) {
        // Note: This is a conceptual implementation
        // Real Gemini API calls for image enhancement would need to be done through a backend
        // due to CORS restrictions and API key security

        const prompt = this.generateEnhancementPrompt(options);

        try {
            // This would be the actual API call structure
            const response = await fetch(`${this.baseUrl}/models/gemini-pro-vision:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [
                            { text: prompt },
                            {
                                inline_data: {
                                    mime_type: "image/jpeg",
                                    data: imageData.split(',')[1] // Remove data:image/jpeg;base64, prefix
                                }
                            }
                        ]
                    }],
                    generationConfig: {
                        temperature: 0.4,
                        topK: 32,
                        topP: 1,
                        maxOutputTokens: 4096,
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`API Error: ${response.status}`);
            }

            const result = await response.json();
            return this.processApiResponse(result, options);

        } catch (error) {
            console.error('Gemini API Error:', error);
            throw new Error(CONFIG.MESSAGES.ERRORS.API_ERROR);
        }
    }

    generateEnhancementPrompt(options) {
        const { enhancementType } = options;

        const prompts = {
            '2x': 'Enhance this image by doubling its resolution while maintaining quality and details. Focus on sharpening edges and improving clarity.',
            '4x': 'Significantly enhance this image by quadrupling its resolution. Apply advanced upscaling techniques to preserve and enhance fine details.',
            'auto': 'Analyze this image and apply the most appropriate enhancement techniques. Improve resolution, clarity, color balance, and overall quality automatically.'
        };

        return prompts[enhancementType] || prompts['auto'];
    }

    processApiResponse(response, options) {
        // Process the API response and return enhanced image data
        // This would extract the enhanced image from the API response
        return {
            enhancedImage: response.candidates[0].content.parts[0].text,
            metadata: {
                enhancementType: options.enhancementType,
                processingTime: Date.now(),
                quality: 'high'
            }
        };
    }
}

// Backend API Service (Recommended Approach)
class BackendAPIService {
    constructor() {
        this.baseUrl = '/api'; // Your backend API endpoint
    }

    async enhanceImage(imageFile, options = {}) {
        const formData = new FormData();
        formData.append('image', imageFile);
        formData.append('enhancementType', options.enhancementType || 'auto');
        formData.append('quality', options.quality || 'high');

        try {
            const response = await fetch(`${this.baseUrl}/enhance-image`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Backend Error: ${response.status}`);
            }

            const result = await response.json();
            return result;

        } catch (error) {
            console.error('Backend API Error:', error);
            throw new Error(CONFIG.MESSAGES.ERRORS.NETWORK);
        }
    }
}
