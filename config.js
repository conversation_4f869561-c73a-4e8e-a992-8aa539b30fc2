// Configuration file for Gemini AI Image Enhancer

// Load environment variables (for backend)
let ENV_VARS = {};
if (typeof process !== 'undefined' && process.env) {
    ENV_VARS = process.env;
} else if (typeof window !== 'undefined') {
    // For frontend, try to load from a global variable set by backend
    ENV_VARS = window.ENV_VARS || {};
}

const CONFIG = {
    // Gemini API Configuration
    GEMINI_API: {
        BASE_URL: 'https://generativelanguage.googleapis.com/v1beta',
        MODEL: 'gemini-pro-vision',
        IMAGE_MODEL: 'imagen-3.0-generate-002'
    },

    // Vertex AI Configuration (Alternative)
    VERTEX_AI: {
        BASE_URL: 'https://us-central1-aiplatform.googleapis.com/v1',
        PROJECT_ID: '', // Will be set by user
        LOCATION: 'us-central1',
        MODEL: 'imagegeneration@006'
    },

    // Application Settings
    APP: {
        MAX_FILE_SIZE: parseInt(ENV_VARS.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
        SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
        PORT: parseInt(ENV_VARS.PORT) || 8000,
        DEBUG: ENV_VARS.DEBUG === 'true' || false,
        ENHANCEMENT_OPTIONS: {
            '2x': {
                name: 'تحسين 2x',
                description: 'مضاعفة الدقة',
                factor: 2
            },
            '4x': {
                name: 'تحسين 4x',
                description: 'أربعة أضعاف الدقة',
                factor: 4
            },
            'auto': {
                name: 'تحسين ذكي',
                description: 'تحسين تلقائي بالذكاء الاصطناعي',
                factor: 'auto'
            }
        }
    },

    // UI Messages in Arabic
    MESSAGES: {
        UPLOAD: {
            DRAG_DROP: 'اسحب وأفلت الصورة هنا',
            CLICK_SELECT: 'أو انقر لاختيار صورة من جهازك',
            INVALID_FORMAT: 'تنسيق الملف غير مدعوم. يرجى اختيار صورة بتنسيق JPG أو PNG أو WebP',
            FILE_TOO_LARGE: 'حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت',
            UPLOAD_SUCCESS: 'تم رفع الصورة بنجاح'
        },
        ENHANCEMENT: {
            PROCESSING: 'جاري تحسين الصورة...',
            SUCCESS: 'تم تحسين الصورة بنجاح!',
            FAILED: 'فشل في تحسين الصورة',
            NO_API_KEY: 'يرجى إدخال مفتاح API أولاً'
        },
        ERRORS: {
            NETWORK: 'خطأ في الاتصال بالإنترنت',
            API_ERROR: 'خطأ في خدمة الذكاء الاصطناعي',
            INVALID_RESPONSE: 'استجابة غير صحيحة من الخادم',
            QUOTA_EXCEEDED: 'تم تجاوز الحد المسموح للاستخدام',
            INVALID_API_KEY: 'مفتاح API غير صحيح'
        }
    },

    // Progress simulation steps
    PROGRESS_STEPS: [
        { progress: 10, message: 'الاتصال بـ Gemini AI...' },
        { progress: 25, message: 'تحليل الصورة بالذكاء الاصطناعي...' },
        { progress: 40, message: 'Gemini يحدد استراتيجية التحسين...' },
        { progress: 55, message: 'تطبيق التحسينات الموجهة بـ AI...' },
        { progress: 70, message: 'تحسين الحدة والتفاصيل...' },
        { progress: 85, message: 'تطبيق فلاتر متقدمة...' },
        { progress: 95, message: 'إنهاء المعالجة...' },
        { progress: 100, message: 'تم التحسين بنجاح!' }
    ],

    // Local Storage Keys
    STORAGE_KEYS: {
        API_KEY: 'gemini_api_key',
        USER_PREFERENCES: 'user_preferences',
        USAGE_STATS: 'usage_stats'
    }
};

// Utility functions
const Utils = {
    // Format file size
    formatFileSize: (bytes) => {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // Get image dimensions
    getImageDimensions: (file) => {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                resolve({ width: img.width, height: img.height });
            };
            img.src = URL.createObjectURL(file);
        });
    },

    // Convert file to base64
    fileToBase64: (file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    },

    // Validate image file
    validateImageFile: (file) => {
        const errors = [];

        if (!CONFIG.APP.SUPPORTED_FORMATS.includes(file.type)) {
            errors.push(CONFIG.MESSAGES.UPLOAD.INVALID_FORMAT);
        }

        if (file.size > CONFIG.APP.MAX_FILE_SIZE) {
            errors.push(CONFIG.MESSAGES.UPLOAD.FILE_TOO_LARGE);
        }

        return errors;
    },

    // Generate unique ID
    generateId: () => {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    },

    // Show notification
    showNotification: (message, type = 'info') => {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    },

    // Save to local storage
    saveToStorage: (key, value) => {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Error saving to storage:', error);
            return false;
        }
    },

    // Load from local storage
    loadFromStorage: (key, defaultValue = null) => {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error loading from storage:', error);
            return defaultValue;
        }
    },

    // Get API key from environment or storage
    getApiKey: () => {
        // Try environment variable first (for backend)
        if (ENV_VARS.GEMINI_API_KEY && ENV_VARS.GEMINI_API_KEY !== 'your_gemini_api_key_here') {
            return ENV_VARS.GEMINI_API_KEY;
        }

        // Fallback to localStorage (for frontend)
        return Utils.loadFromStorage(CONFIG.STORAGE_KEYS.API_KEY);
    },

    // Check if API key is configured
    isApiKeyConfigured: () => {
        const apiKey = Utils.getApiKey();
        return apiKey && apiKey !== 'your_gemini_api_key_here' && apiKey.length > 10;
    },

    // Load environment variables from .env file (for backend)
    loadEnvFile: async () => {
        if (typeof require !== 'undefined') {
            try {
                const dotenv = require('dotenv');
                dotenv.config();
                return true;
            } catch (error) {
                console.warn('dotenv not available, using default config');
                return false;
            }
        }
        return false;
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, Utils };
}
