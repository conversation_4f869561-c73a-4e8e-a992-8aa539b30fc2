# محسن جودة الصور بالذكاء الاصطناعي - جيميني

تطبيق ويب متطور لرفع جودة الصور باستخدام تقنية Google Gemini AI المتقدمة. يوفر التطبيق واجهة مستخدم عربية سهلة الاستخدام مع إمكانيات تحسين متقدمة للصور.

## ✨ الميزات الرئيسية

- 🖼️ **رفع الصور**: سحب وإفلات أو تصفح الملفات
- 🚀 **تحسين متعدد المستويات**: 2x، 4x، أو تحسين ذكي تلقائي
- 🎨 **واجهة عربية حديثة**: تصميم متجاوب وجذاب
- 📱 **متوافق مع الأجهزة المحمولة**: يعمل على جميع الأجهزة
- 💾 **تحميل النتائج**: حفظ الصور المحسنة بسهولة
- 🔒 **آمن**: حفظ مفتاح API محلياً
- ⚡ **سريع**: معالجة فورية مع تقدم مرئي

## 🛠️ التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **AI API**: Google Gemini API مع Imagen 3
- **التصميم**: CSS Grid, Flexbox, Animations
- **الخطوط**: Cairo Font (Google Fonts)
- **الأيقونات**: Font Awesome 6

## 📋 المتطلبات

1. **مفتاح Gemini API**: احصل عليه من [Google AI Studio](https://ai.google.dev/)
2. **متصفح حديث**: Chrome, Firefox, Safari, Edge
3. **اتصال إنترنت**: لاستخدام API

## 🚀 التثبيت والتشغيل

### الطريقة السريعة (موصى بها)
```bash
# تحميل الملفات
git clone https://github.com/your-username/gemini-image-enhancer.git
cd gemini-image-enhancer

# تشغيل التطبيق
python run.py
```

### الطريقة اليدوية

#### 1. تحميل الملفات
```bash
git clone https://github.com/your-username/gemini-image-enhancer.git
cd gemini-image-enhancer
```

#### 2. اختيار طريقة التشغيل

**أ) خادم متقدم مع تحسين حقيقي (موصى به):**
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل الخادم الخلفي
python backend_server.py
```

**ب) خادم بسيط مع تحسين محلي:**
```bash
# باستخدام Python
python -m http.server 8000

# أو باستخدام Node.js
npx http-server

# أو باستخدام Live Server في VS Code
```

#### 3. فتح التطبيق
- **الخادم المتقدم**: `http://localhost:5000`
- **الخادم البسيط**: `http://localhost:8000`

## 🔑 إعداد مفتاح API

1. انتقل إلى [Google AI Studio](https://ai.google.dev/)
2. قم بإنشاء حساب أو تسجيل الدخول
3. احصل على مفتاح API جديد
4. أدخل المفتاح في التطبيق عند المطالبة

## 📖 كيفية الاستخدام

### 1. رفع الصورة
- اسحب وأفلت الصورة في المنطقة المخصصة
- أو انقر على "اختر صورة" لتصفح الملفات
- الصيغ المدعومة: JPG, PNG, WebP
- الحد الأقصى للحجم: 10 ميجابايت

### 2. اختيار نوع التحسين
- **تحسين 2x**: مضاعفة الدقة
- **تحسين 4x**: أربعة أضعاف الدقة
- **تحسين ذكي**: تحسين تلقائي بالذكاء الاصطناعي

### 3. بدء التحسين
- انقر على "ابدأ التحسين"
- انتظر حتى اكتمال المعالجة
- شاهد النتائج في قسم المقارنة

### 4. تحميل النتيجة
- انقر على "تحميل الصورة المحسنة"
- سيتم حفظ الصورة في مجلد التحميلات

## 🏗️ هيكل المشروع

```
gemini-image-enhancer/
├── index.html          # الصفحة الرئيسية
├── style.css           # تنسيقات التطبيق
├── script.js           # منطق التطبيق الرئيسي
├── config.js           # إعدادات التطبيق
├── README.md           # دليل الاستخدام
└── assets/             # الصور والموارد (اختياري)
```

## ⚙️ الإعدادات المتقدمة

### تخصيص الرسائل
يمكنك تعديل الرسائل في ملف `config.js`:

```javascript
MESSAGES: {
    UPLOAD: {
        DRAG_DROP: 'اسحب وأفلت الصورة هنا',
        // ... المزيد من الرسائل
    }
}
```

### تغيير حدود الملفات
```javascript
APP: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    SUPPORTED_FORMATS: ['image/jpeg', 'image/png', 'image/webp']
}
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. لا يعمل رفع الصور**
- تأكد من أن الملف بصيغة مدعومة
- تحقق من حجم الملف (أقل من 10MB)

**2. خطأ في API**
- تأكد من صحة مفتاح API
- تحقق من اتصال الإنترنت
- تأكد من وجود رصيد في حساب Google AI

**3. بطء في التحميل**
- تحقق من سرعة الإنترنت
- جرب صورة أصغر حجماً

## 🔒 الأمان والخصوصية

- مفتاح API يُحفظ محلياً في المتصفح فقط
- الصور لا تُحفظ على الخادم
- جميع العمليات تتم في المتصفح
- لا يتم جمع أي بيانات شخصية

## 🌟 التطوير المستقبلي

- [ ] دعم المزيد من صيغ الصور
- [ ] تحسينات متقدمة (إزالة الضوضاء، تحسين الألوان)
- [ ] معالجة دفعية للصور
- [ ] حفظ الإعدادات المفضلة
- [ ] دعم اللغات الأخرى
- [ ] تطبيق PWA للهواتف

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 📞 الدعم

- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [رابط المشاكل](https://github.com/your-username/gemini-image-enhancer/issues)
- **التوثيق**: [رابط التوثيق](https://github.com/your-username/gemini-image-enhancer/wiki)

## 🙏 شكر وتقدير

- **Google AI**: لتوفير تقنية Gemini المتطورة
- **Font Awesome**: للأيقونات الجميلة
- **Google Fonts**: لخط Cairo العربي
- **المجتمع**: لجميع المساهمات والاقتراحات

---

**ملاحظة**: هذا التطبيق يستخدم Google Gemini API وقد تنطبق عليه شروط الاستخدام الخاصة بـ Google. يرجى مراجعة [شروط الخدمة](https://ai.google.dev/terms) قبل الاستخدام.

**تم تطويره بـ ❤️ للمجتمع العربي**
