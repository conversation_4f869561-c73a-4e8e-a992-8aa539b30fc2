<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكامل التطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        h3 {
            color: #555;
            margin-top: 20px;
        }
        code {
            background: #f8f9fa;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        .file-check {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        .file-check i {
            margin-left: 10px;
            font-size: 18px;
        }
        .check-ok { color: #28a745; }
        .check-error { color: #dc3545; }
        .check-warning { color: #ffc107; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-vial"></i> اختبار تكامل تطبيق محسن الصور</h1>
        
        <div class="test-section">
            <h2><i class="fas fa-file-code"></i> فحص الملفات الأساسية</h2>
            <div id="fileChecks"></div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-cogs"></i> فحص التكامل</h2>
            <div id="integrationChecks"></div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-code"></i> فحص JavaScript</h2>
            <div id="jsChecks"></div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-palette"></i> فحص CSS</h2>
            <div id="cssChecks"></div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-chart-line"></i> ملخص النتائج</h2>
            <div id="summary"></div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="runTests()" style="background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 5px; font-size: 16px; cursor: pointer;">
                <i class="fas fa-play"></i> تشغيل الاختبارات
            </button>
        </div>
    </div>

    <script>
        let testResults = {
            passed: 0,
            failed: 0,
            warnings: 0
        };

        function addResult(containerId, message, type = 'success', icon = 'check') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `file-check`;
            result.innerHTML = `
                <i class="fas fa-${icon} check-${type === 'success' ? 'ok' : type === 'warning' ? 'warning' : 'error'}"></i>
                <span>${message}</span>
            `;
            container.appendChild(result);
            
            if (type === 'success') testResults.passed++;
            else if (type === 'error') testResults.failed++;
            else if (type === 'warning') testResults.warnings++;
        }

        function checkFileExists(filename) {
            return fetch(filename, { method: 'HEAD' })
                .then(response => response.ok)
                .catch(() => false);
        }

        async function runTests() {
            // Reset results
            testResults = { passed: 0, failed: 0, warnings: 0 };
            document.getElementById('fileChecks').innerHTML = '';
            document.getElementById('integrationChecks').innerHTML = '';
            document.getElementById('jsChecks').innerHTML = '';
            document.getElementById('cssChecks').innerHTML = '';
            document.getElementById('summary').innerHTML = '';

            // Test 1: Check required files
            const requiredFiles = [
                'index.html',
                'style.css',
                'script.js',
                'config.js',
                'README.md'
            ];

            for (const file of requiredFiles) {
                const exists = await checkFileExists(file);
                addResult('fileChecks', 
                    `${file} ${exists ? 'موجود' : 'مفقود'}`, 
                    exists ? 'success' : 'error',
                    exists ? 'check' : 'times'
                );
            }

            // Test 2: Check CONFIG object
            try {
                if (typeof CONFIG !== 'undefined') {
                    addResult('integrationChecks', 'كائن CONFIG محمل بنجاح', 'success');
                    
                    if (CONFIG.MESSAGES && CONFIG.MESSAGES.UPLOAD) {
                        addResult('integrationChecks', 'رسائل التطبيق متوفرة', 'success');
                    } else {
                        addResult('integrationChecks', 'رسائل التطبيق مفقودة', 'error', 'times');
                    }
                    
                    if (CONFIG.PROGRESS_STEPS && CONFIG.PROGRESS_STEPS.length > 0) {
                        addResult('integrationChecks', 'خطوات التقدم متوفرة', 'success');
                    } else {
                        addResult('integrationChecks', 'خطوات التقدم مفقودة', 'error', 'times');
                    }
                } else {
                    addResult('integrationChecks', 'كائن CONFIG غير محمل', 'error', 'times');
                }
            } catch (e) {
                addResult('integrationChecks', `خطأ في CONFIG: ${e.message}`, 'error', 'times');
            }

            // Test 3: Check Utils object
            try {
                if (typeof Utils !== 'undefined') {
                    addResult('integrationChecks', 'كائن Utils محمل بنجاح', 'success');
                    
                    const utilFunctions = ['formatFileSize', 'validateImageFile', 'fileToBase64', 'showNotification'];
                    utilFunctions.forEach(func => {
                        if (typeof Utils[func] === 'function') {
                            addResult('integrationChecks', `دالة Utils.${func} متوفرة`, 'success');
                        } else {
                            addResult('integrationChecks', `دالة Utils.${func} مفقودة`, 'error', 'times');
                        }
                    });
                } else {
                    addResult('integrationChecks', 'كائن Utils غير محمل', 'error', 'times');
                }
            } catch (e) {
                addResult('integrationChecks', `خطأ في Utils: ${e.message}`, 'error', 'times');
            }

            // Test 4: Check ImageEnhancer class
            try {
                if (typeof ImageEnhancer !== 'undefined') {
                    addResult('jsChecks', 'فئة ImageEnhancer متوفرة', 'success');
                } else {
                    addResult('jsChecks', 'فئة ImageEnhancer غير متوفرة', 'error', 'times');
                }
            } catch (e) {
                addResult('jsChecks', `خطأ في ImageEnhancer: ${e.message}`, 'error', 'times');
            }

            // Test 5: Check DOM elements
            const requiredElements = [
                'fileInput', 'uploadArea', 'enhanceBtn', 'progressSection', 
                'resultsSection', 'apiKeyModal', 'progressFill', 'progressText'
            ];

            requiredElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    addResult('integrationChecks', `عنصر #${elementId} موجود`, 'success');
                } else {
                    addResult('integrationChecks', `عنصر #${elementId} مفقود`, 'warning', 'exclamation-triangle');
                }
            });

            // Test 6: Check CSS classes
            const requiredClasses = [
                'container', 'upload-area', 'enhance-btn', 'progress-section', 
                'results-section', 'modal', 'notification'
            ];

            requiredClasses.forEach(className => {
                const elements = document.getElementsByClassName(className);
                if (elements.length > 0) {
                    addResult('cssChecks', `فئة CSS .${className} مستخدمة`, 'success');
                } else {
                    addResult('cssChecks', `فئة CSS .${className} غير مستخدمة`, 'warning', 'exclamation-triangle');
                }
            });

            // Summary
            const total = testResults.passed + testResults.failed + testResults.warnings;
            const successRate = Math.round((testResults.passed / total) * 100);
            
            document.getElementById('summary').innerHTML = `
                <div class="test-result ${successRate >= 80 ? 'success' : successRate >= 60 ? 'warning' : 'error'}">
                    <h3><i class="fas fa-chart-pie"></i> نتائج الاختبار</h3>
                    <p><strong>إجمالي الاختبارات:</strong> ${total}</p>
                    <p><strong>نجح:</strong> ${testResults.passed} ✅</p>
                    <p><strong>فشل:</strong> ${testResults.failed} ❌</p>
                    <p><strong>تحذيرات:</strong> ${testResults.warnings} ⚠️</p>
                    <p><strong>معدل النجاح:</strong> ${successRate}%</p>
                    <hr>
                    <p><strong>الحالة العامة:</strong> 
                        ${successRate >= 80 ? '🎉 ممتاز - التطبيق جاهز!' : 
                          successRate >= 60 ? '⚠️ جيد - يحتاج تحسينات طفيفة' : 
                          '❌ يحتاج إصلاحات'}
                    </p>
                </div>
            `;
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
    </script>

    <!-- Load the actual app files for testing -->
    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>
