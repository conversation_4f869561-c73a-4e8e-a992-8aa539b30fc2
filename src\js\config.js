// Configuration Module - Reorganized and Fixed
// وحدة الإعدادات - مُعاد تنظيمها ومُصححة

// Load environment variables (for backend)
let ENV_VARS = {};
if (typeof process !== 'undefined' && process.env) {
    ENV_VARS = process.env;
} else if (typeof window !== 'undefined') {
    // For frontend, try to load from a global variable set by backend
    ENV_VARS = window.ENV_VARS || {};
}

const CONFIG = {
    // Gemini API Configuration
    GEMINI_API: {
        BASE_URL: 'https://generativelanguage.googleapis.com/v1beta',
        MODEL: ENV_VARS.GEMINI_MODEL || 'gemini-2.0-flash',
        TEMPERATURE: parseFloat(ENV_VARS.GEMINI_TEMPERATURE) || 0.4,
        MAX_TOKENS: parseInt(ENV_VARS.GEMINI_MAX_TOKENS) || 4096,
        TOP_K: 32,
        TOP_P: 1
    },

    // Application Settings
    APP: {
        MAX_FILE_SIZE: parseInt(ENV_VARS.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
        SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
        PORT: parseInt(ENV_VARS.PORT) || 8000,
        DEBUG: ENV_VARS.DEBUG === 'true' || false,
        ENHANCEMENT_OPTIONS: {
            '2x': {
                name: 'تحسين 2x',
                description: 'مضاعفة الدقة',
                scale_factor: 2,
                icon: 'fas fa-expand-arrows-alt'
            },
            '4x': {
                name: 'تحسين 4x',
                description: 'تحسين متقدم',
                scale_factor: 4,
                icon: 'fas fa-search-plus'
            },
            'auto': {
                name: 'تحسين ذكي',
                description: 'تحليل تلقائي',
                scale_factor: 2,
                icon: 'fas fa-robot'
            }
        }
    },

    // Storage Keys
    STORAGE_KEYS: {
        API_KEY: 'gemini_api_key',
        USER_PREFERENCES: 'user_preferences',
        LAST_ENHANCEMENT_TYPE: 'last_enhancement_type'
    },

    // Progress Steps
    PROGRESS_STEPS: [
        { progress: 10, message: 'تحليل الصورة...' },
        { progress: 25, message: 'تحضير البيانات...' },
        { progress: 40, message: 'استدعاء Gemini AI...' },
        { progress: 60, message: 'تطبيق التحسينات...' },
        { progress: 80, message: 'معالجة النتائج...' },
        { progress: 95, message: 'إنهاء المعالجة...' },
        { progress: 100, message: 'تم الانتهاء!' }
    ],

    // Messages
    MESSAGES: {
        UPLOAD: {
            UPLOAD_SUCCESS: 'تم رفع الصورة بنجاح',
            INVALID_FILE_TYPE: 'نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG، PNG، أو WebP',
            FILE_TOO_LARGE: 'حجم الملف كبير جداً. الحد الأقصى 10MB',
            EMPTY_FILE: 'الملف فارغ أو تالف'
        },
        ENHANCEMENT: {
            SUCCESS: 'تم تحسين الصورة بنجاح!',
            PROCESSING: 'جاري معالجة الصورة...',
            NO_FILE: 'يرجى اختيار صورة أولاً',
            NO_API_KEY: 'يرجى إدخال مفتاح Gemini API أولاً'
        },
        API: {
            KEY_SAVED: 'تم حفظ مفتاح API بنجاح',
            KEY_REQUIRED: 'يرجى إدخال مفتاح API',
            INVALID_KEY: 'مفتاح API غير صحيح',
            CONNECTION_ERROR: 'خطأ في الاتصال بـ Gemini API'
        },
        ERRORS: {
            GENERAL: 'حدث خطأ غير متوقع',
            NETWORK: 'خطأ في الشبكة',
            API_ERROR: 'خطأ في API',
            PROCESSING_ERROR: 'خطأ في معالجة الصورة',
            BROWSER_NOT_SUPPORTED: 'المتصفح لا يدعم هذه الميزة'
        },
        SUCCESS: {
            DOWNLOAD: 'تم تحميل الصورة بنجاح',
            RESET: 'تم إعادة تعيين التطبيق',
            FILE_REMOVED: 'تم إزالة الملف'
        }
    },

    // Image Processing Settings
    IMAGE_PROCESSING: {
        DEFAULT_QUALITY: 1.0,
        CANVAS_SMOOTHING: true,
        SMOOTHING_QUALITY: 'high',
        FILTERS: {
            SHARPNESS: {
                DEFAULT: 1.2,
                MIN: 1.0,
                MAX: 2.0,
                KERNEL: [0, -1, 0, -1, 5, -1, 0, -1, 0]
            },
            CONTRAST: {
                DEFAULT: 1.1,
                MIN: 0.8,
                MAX: 1.5
            },
            BRIGHTNESS: {
                DEFAULT: 1.05,
                MIN: 0.8,
                MAX: 1.2
            },
            SATURATION: {
                DEFAULT: 1.05,
                MIN: 0.8,
                MAX: 1.3
            }
        }
    },

    // UI Settings
    UI: {
        NOTIFICATION_DURATION: 5000,
        ANIMATION_DURATION: 300,
        PROGRESS_UPDATE_INTERVAL: 1000,
        THEMES: {
            DEFAULT: 'light',
            AVAILABLE: ['light', 'dark']
        }
    },

    // API Endpoints
    ENDPOINTS: {
        ENHANCE_IMAGE: '/api/enhance-image',
        VALIDATE_API_KEY: '/api/validate-key',
        HEALTH_CHECK: '/api/health'
    }
};

// Utility Functions
const ConfigUtils = {
    // Get environment variable with fallback
    getEnvVar: (key, defaultValue = null) => {
        return ENV_VARS[key] || defaultValue;
    },

    // Get API key from environment or storage
    getApiKey: () => {
        // Try environment variable first (for backend)
        if (ENV_VARS.GEMINI_API_KEY && ENV_VARS.GEMINI_API_KEY !== 'your_gemini_api_key_here') {
            return ENV_VARS.GEMINI_API_KEY;
        }
        
        // Fallback to localStorage (for frontend)
        if (typeof localStorage !== 'undefined') {
            try {
                const stored = localStorage.getItem(CONFIG.STORAGE_KEYS.API_KEY);
                return stored ? JSON.parse(stored) : null;
            } catch (error) {
                console.error('Error loading API key from storage:', error);
                return null;
            }
        }
        
        return null;
    },

    // Check if API key is configured
    isApiKeyConfigured: () => {
        const apiKey = ConfigUtils.getApiKey();
        return apiKey && apiKey !== 'your_gemini_api_key_here' && apiKey.length > 10;
    },

    // Get enhancement option details
    getEnhancementOption: (type) => {
        return CONFIG.APP.ENHANCEMENT_OPTIONS[type] || CONFIG.APP.ENHANCEMENT_OPTIONS['auto'];
    },

    // Get supported file types for input accept attribute
    getSupportedFileTypes: () => {
        return CONFIG.APP.SUPPORTED_FORMATS.join(',');
    },

    // Format file size
    formatFileSize: (bytes) => {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // Validate file
    validateFile: (file) => {
        const errors = [];
        
        if (!file) {
            errors.push(CONFIG.MESSAGES.UPLOAD.EMPTY_FILE);
            return errors;
        }
        
        // Check file type
        if (!CONFIG.APP.SUPPORTED_FORMATS.includes(file.type)) {
            errors.push(CONFIG.MESSAGES.UPLOAD.INVALID_FILE_TYPE);
        }
        
        // Check file size
        if (file.size > CONFIG.APP.MAX_FILE_SIZE) {
            errors.push(CONFIG.MESSAGES.UPLOAD.FILE_TOO_LARGE);
        }
        
        // Check if file is empty
        if (file.size === 0) {
            errors.push(CONFIG.MESSAGES.UPLOAD.EMPTY_FILE);
        }
        
        return errors;
    },

    // Save to localStorage
    saveToStorage: (key, value) => {
        if (typeof localStorage === 'undefined') return false;
        
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Error saving to storage:', error);
            return false;
        }
    },

    // Load from localStorage
    loadFromStorage: (key, defaultValue = null) => {
        if (typeof localStorage === 'undefined') return defaultValue;
        
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error loading from storage:', error);
            return defaultValue;
        }
    },

    // Generate unique ID
    generateId: () => {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    },

    // Debug log (only if debug mode is enabled)
    debugLog: (...args) => {
        if (CONFIG.APP.DEBUG) {
            console.log('[DEBUG]', ...args);
        }
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, ConfigUtils };
} else {
    window.CONFIG = CONFIG;
    window.ConfigUtils = ConfigUtils;
}
