<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محسن الصور بالذكاء الاصطناعي - Gemini AI Image Enhancer</title>
    <meta name="description" content="تطبيق متقدم لتحسين جودة الصور باستخدام الذكاء الاصطناعي من Google Gemini">
    <meta name="keywords" content="تحسين الصور, ذكاء اصطناعي, Gemini AI, تكبير الصور, جودة عالية">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🖼️</text></svg>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="src/css/styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1><i class="fas fa-magic"></i> محسن الصور بالذكاء الاصطناعي</h1>
            <p>تطبيق متقدم لتحسين جودة الصور باستخدام تقنيات الذكاء الاصطناعي من Google Gemini</p>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        
        <!-- Upload Section -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-cloud-upload-alt"></i>
                    رفع الصورة
                </h2>
            </div>
            
            <div class="upload-area" id="uploadArea">
                <div class="upload-content">
                    <i class="fas fa-image upload-icon"></i>
                    <div class="upload-text">اسحب وأفلت الصورة هنا أو انقر للاختيار</div>
                    <div class="upload-hint">يدعم JPG, PNG, WebP - حتى 10MB</div>
                </div>
                <input type="file" id="fileInput" class="file-input" accept="image/*">
            </div>
            
            <!-- File Info -->
            <div class="file-info" id="fileInfo">
                <div class="file-details">
                    <i class="fas fa-file-image file-icon"></i>
                    <div class="file-meta">
                        <h4 id="fileName">اسم الملف</h4>
                        <p id="fileSize">حجم الملف</p>
                    </div>
                </div>
                <button class="btn btn-danger btn-icon" id="removeBtn" title="إزالة الملف">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Enhancement Options -->
        <div class="card" id="optionsSection" style="display: none;">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-sliders-h"></i>
                    خيارات التحسين
                </h2>
            </div>
            
            <div class="enhancement-options">
                <label class="option-card" for="enhance2x">
                    <input type="radio" name="enhancement" value="2x" id="enhance2x" class="option-radio" checked>
                    <div class="option-content">
                        <i class="fas fa-expand-arrows-alt option-icon"></i>
                        <div class="option-title">تحسين 2x</div>
                        <div class="option-description">مضاعفة الدقة مع تحسين الجودة</div>
                    </div>
                </label>
                
                <label class="option-card" for="enhance4x">
                    <input type="radio" name="enhancement" value="4x" id="enhance4x" class="option-radio">
                    <div class="option-content">
                        <i class="fas fa-search-plus option-icon"></i>
                        <div class="option-title">تحسين 4x</div>
                        <div class="option-description">تحسين متقدم بدقة عالية جداً</div>
                    </div>
                </label>
                
                <label class="option-card" for="enhanceAuto">
                    <input type="radio" name="enhancement" value="auto" id="enhanceAuto" class="option-radio">
                    <div class="option-content">
                        <i class="fas fa-robot option-icon"></i>
                        <div class="option-title">تحسين ذكي</div>
                        <div class="option-description">تحليل تلقائي وتحسين مثالي</div>
                    </div>
                </label>
            </div>
            
            <div class="text-center mt-3">
                <button class="btn btn-primary" id="enhanceBtn">
                    <i class="fas fa-magic"></i>
                    بدء التحسين
                </button>
            </div>
        </div>

        <!-- Progress Section -->
        <div class="card" id="progressSection" style="display: none;">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-cog fa-spin"></i>
                    جاري المعالجة
                </h2>
            </div>
            
            <div class="progress-container">
                <div class="progress-info">
                    <span>التقدم:</span>
                    <span class="progress-text" id="progressText">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-message" id="progressMessage">جاري التحضير...</div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="card" id="resultsSection" style="display: none;">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-check-circle"></i>
                    النتائج
                </h2>
                <div>
                    <button class="btn btn-success" id="downloadBtn">
                        <i class="fas fa-download"></i>
                        تحميل الصورة
                    </button>
                </div>
            </div>
            
            <div class="results-grid">
                <div class="result-item">
                    <img id="originalImage" class="result-image" alt="الصورة الأصلية">
                    <div class="result-label">الصورة الأصلية</div>
                    <div class="result-size" id="originalSize">-</div>
                </div>
                
                <div class="result-item">
                    <img id="enhancedImage" class="result-image" alt="الصورة المحسنة">
                    <div class="result-label">الصورة المحسنة</div>
                    <div class="result-size" id="enhancedSize">-</div>
                </div>
            </div>
        </div>

        <!-- Error Section -->
        <div class="card" id="errorSection" style="display: none;">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    حدث خطأ
                </h2>
            </div>
            
            <div class="text-center">
                <p id="errorMessage" class="mb-2">حدث خطأ غير متوقع</p>
                <button class="btn btn-primary" id="retryBtn">
                    <i class="fas fa-redo"></i>
                    إعادة المحاولة
                </button>
            </div>
        </div>

        <!-- Reset Button -->
        <div class="text-center mt-3">
            <button class="btn btn-secondary" id="resetBtn">
                <i class="fas fa-refresh"></i>
                إعادة تعيين
            </button>
        </div>
    </div>

    <!-- API Key Modal -->
    <div class="modal" id="apiKeyModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-key"></i>
                    إدخال مفتاح Gemini API
                </h3>
            </div>
            
            <div class="form-group">
                <label for="apiKeyInput" class="form-label">مفتاح API:</label>
                <input type="password" id="apiKeyInput" class="form-input" 
                       placeholder="أدخل مفتاح Gemini API الخاص بك">
                <small style="color: #718096; font-size: 0.8rem; margin-top: 5px; display: block;">
                    يمكنك الحصول على مفتاح مجاني من 
                    <a href="https://ai.google.dev/" target="_blank" style="color: #667eea;">Google AI Studio</a>
                </small>
            </div>
            
            <div class="modal-actions">
                <button class="btn btn-primary" id="saveApiKey">
                    <i class="fas fa-save"></i>
                    حفظ
                </button>
                <button class="btn btn-secondary" id="cancelApiKey">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="src/js/utils.js"></script>
    <script src="src/js/imageProcessor.js"></script>
    <script src="src/js/geminiAPI.js"></script>
    <script src="src/js/app.js"></script>

    <!-- Enhanced Radio Button Styles -->
    <script>
        // تحديث أنماط الخيارات عند التحديد
        document.addEventListener('DOMContentLoaded', function() {
            const radioButtons = document.querySelectorAll('input[name="enhancement"]');
            const optionCards = document.querySelectorAll('.option-card');
            
            function updateSelection() {
                optionCards.forEach(card => card.classList.remove('selected'));
                radioButtons.forEach(radio => {
                    if (radio.checked) {
                        radio.closest('.option-card').classList.add('selected');
                    }
                });
            }
            
            radioButtons.forEach(radio => {
                radio.addEventListener('change', updateSelection);
            });
            
            // تحديد الخيار الافتراضي
            updateSelection();
        });
    </script>
</body>
</html>
