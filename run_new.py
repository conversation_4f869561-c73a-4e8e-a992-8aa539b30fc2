#!/usr/bin/env python3
"""
Enhanced Quick Start Script for Gemini AI Image Enhancer
سكريبت البدء السريع المحسن لمحسن الصور بـ Gemini AI
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading
from pathlib import Path

def print_banner():
    """Print application banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🖼️  محسن الصور بالذكاء الاصطناعي - Gemini AI        ║
    ║                                                              ║
    ║                    Enhanced & Reorganized                    ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} متوفر")

def check_files():
    """Check for required files"""
    required_files = [
        "index_new.html",
        "src/css/styles.css",
        "src/js/app.js",
        "src/js/utils.js",
        "src/js/imageProcessor.js",
        "src/js/geminiAPI.js",
        "src/js/config.js",
        "src/backend/server.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    
    # Check for .env file
    check_env_file()
    
    return True

def check_env_file():
    """Check for environment file"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("⚠️  ملف .env غير موجود")
            print("💡 يمكنك نسخه من .env.example:")
            print("   cp .env.example .env")
            print("   ثم أدخل مفتاح Gemini API الخاص بك")
        else:
            print("⚠️  ملفات .env و .env.example غير موجودة")
    else:
        print("✅ ملف .env موجود")
        
        # Check if API key is set
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'GEMINI_API_KEY=your_gemini_api_key_here' in content:
                    print("⚠️  يرجى تحديث مفتاح Gemini API في ملف .env")
                elif 'GEMINI_API_KEY=' in content and len(content.split('GEMINI_API_KEY=')[1].split('\n')[0].strip()) > 10:
                    print("✅ مفتاح Gemini API مُعرف في ملف .env")
                else:
                    print("⚠️  مفتاح Gemini API غير مُعرف في ملف .env")
        except Exception as e:
            print(f"⚠️  خطأ في قراءة ملف .env: {e}")

def install_requirements():
    """Install Python requirements"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("⚠️  ملف requirements.txt غير موجود")
        return True
    
    try:
        print("📦 تثبيت المتطلبات...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def open_browser_delayed(url, delay=3):
    """Open browser after delay"""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        print(f"🌐 تم فتح المتصفح: {url}")
    except Exception as e:
        print(f"⚠️  فشل في فتح المتصفح: {e}")
        print(f"🔗 افتح الرابط يدوياً: {url}")

def run_simple_server(port=8000):
    """Run simple HTTP server"""
    try:
        print(f"🚀 بدء الخادم البسيط على المنفذ {port}...")
        print(f"🔗 الرابط: http://localhost:{port}")
        
        # Open browser in background
        browser_thread = threading.Thread(target=open_browser_delayed, args=(f"http://localhost:{port}",))
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start server
        subprocess.run([sys.executable, "-m", "http.server", str(port)], check=True)
        
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف الخادم")
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تشغيل الخادم: {e}")

def run_backend_server():
    """Run enhanced backend server"""
    try:
        print("🚀 بدء الخادم الخلفي المحسن...")
        
        # Check if Flask is installed
        try:
            import flask
            print("✅ Flask متوفر")
        except ImportError:
            print("📦 تثبيت Flask...")
            subprocess.run([sys.executable, "-m", "pip", "install", "Flask", "Flask-CORS"], check=True)
        
        # Run backend server
        backend_path = Path("src/backend/server.py")
        if backend_path.exists():
            print(f"🔗 الرابط: http://localhost:5000")
            
            # Open browser in background
            browser_thread = threading.Thread(target=open_browser_delayed, args=("http://localhost:5000",))
            browser_thread.daemon = True
            browser_thread.start()
            
            subprocess.run([sys.executable, str(backend_path)], check=True)
        else:
            print("❌ ملف الخادم الخلفي غير موجود")
            
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ فشل في تشغيل الخادم الخلفي: {e}")

def show_menu():
    """Show main menu"""
    menu = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                         خيارات التشغيل                        ║
    ╠══════════════════════════════════════════════════════════════╣
    ║                                                              ║
    ║  1️⃣  تشغيل الخادم الخلفي المحسن (مُوصى به)                   ║
    ║      • دعم كامل لـ Gemini AI                                 ║
    ║      • معالجة متقدمة للصور                                   ║
    ║      • أداء محسن                                            ║
    ║                                                              ║
    ║  2️⃣  تشغيل سريع (خادم بسيط)                                 ║
    ║      • تشغيل فوري                                           ║
    ║      • معالجة محلية فقط                                      ║
    ║      • مناسب للاختبار السريع                                 ║
    ║                                                              ║
    ║  3️⃣  تثبيت المتطلبات فقط                                     ║
    ║                                                              ║
    ║  4️⃣  فحص الملفات والإعدادات                                  ║
    ║                                                              ║
    ║  5️⃣  خروج                                                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(menu)

def main():
    """Main function"""
    print_banner()
    
    # Check Python version
    check_python_version()
    
    while True:
        show_menu()
        
        try:
            choice = input("اختر رقماً (1-5): ").strip()
            
            if choice == '1':
                print("\n" + "="*60)
                print("🚀 تشغيل الخادم الخلفي المحسن...")
                print("="*60)
                
                if not check_files():
                    print("❌ يرجى التأكد من وجود جميع الملفات المطلوبة")
                    continue
                
                if not install_requirements():
                    print("❌ فشل في تثبيت المتطلبات")
                    continue
                
                run_backend_server()
                
            elif choice == '2':
                print("\n" + "="*60)
                print("🚀 تشغيل سريع...")
                print("="*60)
                
                if not check_files():
                    print("❌ يرجى التأكد من وجود جميع الملفات المطلوبة")
                    continue
                
                run_simple_server()
                
            elif choice == '3':
                print("\n" + "="*60)
                print("📦 تثبيت المتطلبات...")
                print("="*60)
                
                if install_requirements():
                    print("✅ تم تثبيت جميع المتطلبات بنجاح")
                else:
                    print("❌ فشل في تثبيت بعض المتطلبات")
                
                input("\nاضغط Enter للمتابعة...")
                
            elif choice == '4':
                print("\n" + "="*60)
                print("🔍 فحص الملفات والإعدادات...")
                print("="*60)
                
                check_files()
                
                input("\nاضغط Enter للمتابعة...")
                
            elif choice == '5':
                print("\n👋 شكراً لاستخدام محسن الصور بالذكاء الاصطناعي!")
                break
                
            else:
                print("❌ خيار غير صحيح. يرجى اختيار رقم من 1 إلى 5")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
