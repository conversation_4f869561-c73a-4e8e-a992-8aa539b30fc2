// Utility Functions Module
// وحدة الدوال المساعدة

class Utils {
    // تحويل الملف إلى base64
    static fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
            reader.readAsDataURL(file);
        });
    }

    // الحصول على أبعاد الصورة
    static getImageDimensions(file) {
        return new Promise((resolve, reject) => {
            if (typeof file === 'string') {
                // إذا كان الملف عبارة عن URL أو base64
                const img = new Image();
                img.onload = () => resolve({ width: img.width, height: img.height });
                img.onerror = () => reject(new Error('فشل في تحميل الصورة'));
                img.src = file;
            } else {
                // إذا كان الملف عبارة عن File object
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => resolve({ width: img.width, height: img.height });
                    img.onerror = () => reject(new Error('فشل في تحميل الصورة'));
                    img.src = e.target.result;
                };
                reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
                reader.readAsDataURL(file);
            }
        });
    }

    // التحقق من صحة ملف الصورة
    static validateImageFile(file) {
        const errors = [];
        
        // التحقق من نوع الملف
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            errors.push('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG، PNG، أو WebP');
        }
        
        // التحقق من حجم الملف (10MB)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            errors.push('حجم الملف كبير جداً. الحد الأقصى 10MB');
        }
        
        // التحقق من وجود الملف
        if (!file || file.size === 0) {
            errors.push('الملف فارغ أو تالف');
        }
        
        return errors;
    }

    // تنسيق حجم الملف
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // حفظ البيانات في localStorage
    static saveToStorage(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    }

    // تحميل البيانات من localStorage
    static loadFromStorage(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return defaultValue;
        }
    }

    // الحصول على مفتاح API
    static getApiKey() {
        // محاولة الحصول على المفتاح من متغيرات البيئة أولاً
        if (typeof window !== 'undefined' && window.ENV_VARS && window.ENV_VARS.GEMINI_API_KEY) {
            return window.ENV_VARS.GEMINI_API_KEY;
        }
        
        // العودة إلى localStorage
        return Utils.loadFromStorage('gemini_api_key');
    }

    // التحقق من تكوين مفتاح API
    static isApiKeyConfigured() {
        const apiKey = Utils.getApiKey();
        return apiKey && apiKey !== 'your_gemini_api_key_here' && apiKey.length > 10;
    }

    // عرض الإشعارات
    static showNotification(message, type = 'info', duration = 5000) {
        // إزالة الإشعارات السابقة
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());

        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button onclick="this.parentElement.remove()" style="
                background: none; 
                border: none; 
                color: white; 
                font-size: 18px; 
                cursor: pointer;
                margin-left: 10px;
            ">×</button>
        `;

        // إضافة الإشعار إلى الصفحة
        document.body.appendChild(notification);

        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }

    // إنشاء معرف فريد
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    // تحميل ملف
    static downloadFile(dataUrl, filename) {
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // تأخير التنفيذ
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // التحقق من دعم المتصفح للميزات المطلوبة
    static checkBrowserSupport() {
        const features = {
            canvas: !!document.createElement('canvas').getContext,
            fileReader: !!window.FileReader,
            localStorage: !!window.localStorage,
            fetch: !!window.fetch
        };

        const unsupported = Object.keys(features).filter(feature => !features[feature]);
        
        if (unsupported.length > 0) {
            Utils.showNotification(
                `المتصفح لا يدعم الميزات التالية: ${unsupported.join(', ')}`,
                'error'
            );
            return false;
        }

        return true;
    }

    // تنظيف البيانات المؤقتة
    static cleanup() {
        // إزالة الإشعارات
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach(notification => notification.remove());

        // تنظيف canvas elements المؤقتة
        const tempCanvases = document.querySelectorAll('canvas[data-temp="true"]');
        tempCanvases.forEach(canvas => canvas.remove());
    }

    // تحويل RGB إلى HSL
    static rgbToHsl(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0; // achromatic
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return [h * 360, s * 100, l * 100];
    }

    // تحويل HSL إلى RGB
    static hslToRgb(h, s, l) {
        h /= 360;
        s /= 100;
        l /= 100;

        const hue2rgb = (p, q, t) => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        };

        let r, g, b;

        if (s === 0) {
            r = g = b = l; // achromatic
        } else {
            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;
            r = hue2rgb(p, q, h + 1/3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1/3);
        }

        return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
    }
}

// تصدير الفئة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Utils;
} else {
    window.Utils = Utils;
}
