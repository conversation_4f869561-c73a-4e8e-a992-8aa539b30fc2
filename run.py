#!/usr/bin/env python3
"""
ملف تشغيل سريع لتطبيق تحسين الصور
Quick start script for Image Enhancement App
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} متوفر")

def install_requirements():
    """تثبيت المتطلبات"""
    requirements_file = Path("requirements.txt")

    if not requirements_file.exists():
        print("⚠️  ملف requirements.txt غير موجود")
        return False

    print("📦 جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def check_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        "index.html",
        "style.css",
        "script.js",
        "config.js",
        "backend_server.py"
    ]

    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)

    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False

    print("✅ جميع الملفات المطلوبة موجودة")

    # Check for .env file
    check_env_file()

    return True

def check_env_file():
    """التحقق من ملف متغيرات البيئة"""
    env_file = Path(".env")
    env_example = Path(".env.example")

    if not env_file.exists():
        if env_example.exists():
            print("⚠️  ملف .env غير موجود")
            print("💡 يمكنك نسخه من .env.example:")
            print("   cp .env.example .env")
            print("   ثم أدخل مفتاح Gemini API الخاص بك")
        else:
            print("⚠️  ملفات .env و .env.example غير موجودة")
    else:
        print("✅ ملف .env موجود")

        # Check if API key is set
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'GEMINI_API_KEY=your_gemini_api_key_here' in content:
                    print("⚠️  يرجى تحديث مفتاح Gemini API في ملف .env")
                elif 'GEMINI_API_KEY=' in content and len(content.split('GEMINI_API_KEY=')[1].split('\n')[0].strip()) > 10:
                    print("✅ مفتاح Gemini API مُعرف في ملف .env")
                else:
                    print("⚠️  مفتاح Gemini API غير مُعرف في ملف .env")
        except Exception as e:
            print(f"⚠️  خطأ في قراءة ملف .env: {e}")

def open_browser_delayed(url, delay=3):
    """فتح المتصفح بعد تأخير"""
    time.sleep(delay)
    webbrowser.open(url)

def run_backend_server():
    """تشغيل الخادم الخلفي"""
    try:
        print("🚀 بدء تشغيل الخادم الخلفي...")

        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(
            target=open_browser_delayed,
            args=("http://localhost:5000",)
        )
        browser_thread.daemon = True
        browser_thread.start()

        # تشغيل الخادم
        subprocess.run([sys.executable, "backend_server.py"])

    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def run_simple_server():
    """تشغيل خادم بسيط بدون backend"""
    try:
        print("🚀 بدء تشغيل الخادم البسيط...")

        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(
            target=open_browser_delayed,
            args=("http://localhost:8000",)
        )
        browser_thread.daemon = True
        browser_thread.start()

        # تشغيل خادم HTTP بسيط
        subprocess.run([sys.executable, "-m", "http.server", "8000"])

    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def print_banner():
    """طباعة شعار التطبيق"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🎨 محسن جودة الصور بالذكاء الاصطناعي - جيميني        ║
    ║                                                              ║
    ║                Gemini AI Image Enhancer                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """الدالة الرئيسية"""
    print_banner()

    # التحقق من إصدار Python
    check_python_version()

    # التحقق من الملفات
    if not check_files():
        sys.exit(1)

    print("\n🔧 اختر طريقة التشغيل:")
    print("1. خادم متقدم مع تحسين حقيقي للصور (يتطلب تثبيت المتطلبات)")
    print("2. خادم بسيط مع تحسين محلي فقط")
    print("3. تثبيت المتطلبات فقط")
    print("4. خروج")

    choice = input("\nأدخل اختيارك (1-4): ").strip()

    if choice == "1":
        print("\n📦 التحقق من المتطلبات...")
        if install_requirements():
            run_backend_server()
        else:
            print("❌ فشل في تثبيت المتطلبات. جاري التشغيل بالوضع البسيط...")
            run_simple_server()

    elif choice == "2":
        run_simple_server()

    elif choice == "3":
        install_requirements()

    elif choice == "4":
        print("👋 وداعاً!")
        sys.exit(0)

    else:
        print("❌ اختيار غير صحيح")
        main()

if __name__ == "__main__":
    main()
