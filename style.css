/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.header-content {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4a5568;
    margin-bottom: 10px;
}

.title i {
    color: #667eea;
    margin-left: 15px;
}

.subtitle {
    font-size: 1.2rem;
    color: #718096;
    font-weight: 400;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Upload Section */
.upload-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.upload-area {
    border: 3px dashed #cbd5e0;
    border-radius: 15px;
    padding: 60px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: #f7fafc;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: #667eea;
    background: #edf2f7;
    transform: translateY(-2px);
}

.upload-icon {
    font-size: 4rem;
    color: #a0aec0;
    margin-bottom: 20px;
}

.upload-area h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 10px;
}

.upload-area p {
    color: #718096;
    margin-bottom: 20px;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.upload-btn i {
    margin-left: 10px;
}

/* File Info */
.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #e6fffa;
    padding: 15px 20px;
    border-radius: 10px;
    margin-top: 20px;
    border: 1px solid #81e6d9;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #234e52;
}

.file-details i {
    color: #38b2ac;
}

.remove-btn {
    background: #fed7d7;
    color: #c53030;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #feb2b2;
}

/* Options Section */
.options-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.options-section h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.options-section h3 i {
    color: #667eea;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.option-card {
    position: relative;
}

.option-card input[type="radio"] {
    display: none;
}

.option-card label {
    display: block;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option-card label:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.option-card input[type="radio"]:checked + label {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

.option-card label i {
    font-size: 2rem;
    margin-bottom: 15px;
    display: block;
}

.option-card label span {
    font-size: 1.2rem;
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

.option-card label small {
    font-size: 0.9rem;
    opacity: 0.8;
}

.enhance-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 18px 40px;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    display: block;
}

.enhance-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
}

.enhance-btn i {
    margin-left: 10px;
}

/* Progress Section */
.progress-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.progress-info {
    margin-bottom: 30px;
}

.progress-info i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.progress-info h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 10px;
}

.progress-info p {
    color: #718096;
}

.progress-bar {
    background: #e2e8f0;
    border-radius: 10px;
    height: 12px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    background: linear-gradient(135deg, #667eea, #764ba2);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #4a5568;
}

/* Results Section */
.results-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.results-section h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    text-align: center;
    justify-content: center;
}

.results-section h3 i {
    color: #f6ad55;
}

.comparison-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.image-container h4 {
    text-align: center;
    margin-bottom: 15px;
    color: #4a5568;
    font-size: 1.2rem;
}

.image-wrapper {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.image-wrapper img {
    width: 100%;
    height: auto;
    display: block;
}

.image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    text-align: center;
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.download-btn, .new-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.download-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.new-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.download-btn:hover, .new-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.download-btn i, .new-btn i {
    margin-left: 8px;
}

/* Error Section */
.error-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.error-container i {
    font-size: 3rem;
    color: #e53e3e;
    margin-bottom: 15px;
}

.error-container h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 10px;
}

.error-container p {
    color: #718096;
    margin-bottom: 20px;
}

.retry-btn {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);
}

.retry-btn i {
    margin-left: 8px;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.footer p {
    margin-bottom: 5px;
    font-weight: 600;
}

.footer small {
    font-size: 0.8rem;
    opacity: 0.7;
    display: block;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
}

.modal-content h3 {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-content h3 i {
    color: #667eea;
}

.modal-content p {
    color: #718096;
    margin-bottom: 20px;
}

#apiKeyInput {
    width: 100%;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    margin-bottom: 20px;
    font-family: 'Cairo', sans-serif;
}

#apiKeyInput:focus {
    outline: none;
    border-color: #667eea;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.save-btn, .cancel-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.save-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.cancel-btn {
    background: #e2e8f0;
    color: #4a5568;
}

.save-btn:hover, .cancel-btn:hover {
    transform: translateY(-1px);
}

.api-help {
    margin-top: 20px;
    padding: 15px;
    background: #edf2f7;
    border-radius: 10px;
    font-size: 0.9rem;
    color: #4a5568;
}

.api-help a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.api-help a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .title {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .upload-area {
        padding: 40px 15px;
    }

    .options-grid {
        grid-template-columns: 1fr;
    }

    .comparison-container {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .modal-content {
        padding: 30px 20px;
    }

    .modal-buttons {
        flex-direction: column;
    }
}
