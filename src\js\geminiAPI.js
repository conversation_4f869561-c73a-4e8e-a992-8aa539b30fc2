// Gemini AI API Integration Module
// وحدة تكامل Gemini AI

class GeminiAPI {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
        this.model = 'gemini-2.0-flash';
    }

    // تحليل الصورة باستخدام Gemini AI
    async analyzeImage(imageBase64, enhancementType) {
        if (!this.apiKey) {
            throw new Error('مفتاح Gemini API غير متوفر');
        }

        try {
            const prompt = this.generateAnalysisPrompt(enhancementType);
            const imageData = imageBase64.split(',')[1]; // إزالة البادئة
            const mimeType = imageBase64.split(';')[0].split(':')[1];

            const requestBody = {
                contents: [{
                    parts: [
                        { text: prompt },
                        {
                            inline_data: {
                                mime_type: mimeType,
                                data: imageData
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.4,
                    topK: 32,
                    topP: 1,
                    maxOutputTokens: 4096,
                }
            };

            const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Gemini API Error: ${response.status} - ${errorData.error?.message || response.statusText}`);
            }

            const result = await response.json();
            
            if (result.candidates && result.candidates.length > 0) {
                const responseText = result.candidates[0].content.parts[0].text;
                return this.parseAnalysisResponse(responseText);
            } else {
                throw new Error('لا توجد استجابة من Gemini API');
            }

        } catch (error) {
            console.error('Gemini API Error:', error);
            throw error;
        }
    }

    // إنشاء prompt للتحليل
    generateAnalysisPrompt(enhancementType) {
        const prompts = {
            '2x': `تحليل هذه الصورة وتقديم توصيات مفصلة لتحسينها بدقة 2x.
                   ركز على: تحسين الحدة، تعزيز التباين، تقليل الضوضاء، والحفاظ على التفاصيل.
                   اقترح قيم محددة للحدة (1.0-2.0)، التباين (0.8-1.5)، السطوع (0.8-1.2)،
                   والتشبع (0.8-1.3). وحدد ما إذا كانت الصورة تحتاج لتقليل الضوضاء أو تحسين التفاصيل.`,

            '4x': `تحليل هذه الصورة لتحسينها بدقة 4x. قدم توصيات شاملة لـ:
                   الحفاظ على التفاصيل بأقصى درجة، تقليل الضوضاء المتقدم، تحسين الحواف، تعزيز الألوان،
                   وإزالة العيوب. اقترح قيم مثلى لجميع معاملات التحسين وحدد
                   المناطق المحددة التي تحتاج انتباه (الوجوه، النصوص، الملمس، الحواف).`,

            'auto': `قم بتحليل ذكي لهذه الصورة لتحديد أفضل استراتيجية تحسين.
                     قيم جودة الصورة، حدد المشاكل (الضبابية، الضوضاء، التباين المنخفض، الإضاءة السيئة)،
                     واقترح النهج الأنسب للتحسين. قدم قيم معاملات محددة
                     واشرح السبب وراء كل قرار تحسين.`
        };

        return prompts[enhancementType] || prompts['auto'];
    }

    // تحليل استجابة Gemini
    parseAnalysisResponse(responseText) {
        const instructions = {
            sharpness: 1.2,
            contrast: 1.1,
            brightness: 1.0,
            saturation: 1.05,
            noiseReduction: false,
            detailEnhancement: true
        };

        const text = responseText.toLowerCase();

        // تحليل قيم الحدة
        const sharpnessMatch = text.match(/(?:حدة|sharpness)[:\s]*([0-9.]+)/);
        if (sharpnessMatch) {
            instructions.sharpness = Math.min(2.0, Math.max(1.0, parseFloat(sharpnessMatch[1])));
        } else if (text.includes('حاد جداً') || text.includes('very sharp')) {
            instructions.sharpness = 1.8;
        } else if (text.includes('حاد') || text.includes('sharp')) {
            instructions.sharpness = 1.5;
        }

        // تحليل قيم التباين
        const contrastMatch = text.match(/(?:تباين|contrast)[:\s]*([0-9.]+)/);
        if (contrastMatch) {
            instructions.contrast = Math.min(1.5, Math.max(0.8, parseFloat(contrastMatch[1])));
        } else if (text.includes('تباين عالي') || text.includes('high contrast')) {
            instructions.contrast = 1.3;
        }

        // تحليل قيم السطوع
        const brightnessMatch = text.match(/(?:سطوع|brightness)[:\s]*([0-9.]+)/);
        if (brightnessMatch) {
            instructions.brightness = Math.min(1.2, Math.max(0.8, parseFloat(brightnessMatch[1])));
        } else if (text.includes('مشرق') || text.includes('bright')) {
            instructions.brightness = 1.1;
        } else if (text.includes('مظلم') || text.includes('dark')) {
            instructions.brightness = 1.15;
        }

        // تحليل قيم التشبع
        const saturationMatch = text.match(/(?:تشبع|saturation)[:\s]*([0-9.]+)/);
        if (saturationMatch) {
            instructions.saturation = Math.min(1.3, Math.max(0.8, parseFloat(saturationMatch[1])));
        } else if (text.includes('ألوان زاهية') || text.includes('vibrant')) {
            instructions.saturation = 1.2;
        }

        // تحليل الحاجة لتقليل الضوضاء
        if (text.includes('ضوضاء') || text.includes('noise') || text.includes('تشويش')) {
            instructions.noiseReduction = true;
        }

        // تحليل الحاجة لتحسين التفاصيل
        if (text.includes('تفاصيل') || text.includes('detail') || text.includes('ملمس')) {
            instructions.detailEnhancement = true;
        }

        console.log('Gemini AI Analysis Results:', instructions);
        return instructions;
    }

    // التحقق من صحة مفتاح API
    async validateApiKey() {
        try {
            const testPrompt = "مرحبا";
            const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{ text: testPrompt }]
                    }]
                })
            });

            return response.ok;
        } catch (error) {
            return false;
        }
    }
}

// تصدير الفئة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GeminiAPI;
} else {
    window.GeminiAPI = GeminiAPI;
}
